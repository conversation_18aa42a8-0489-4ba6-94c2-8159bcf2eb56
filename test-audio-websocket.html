<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhisperSync WebSocket 音频测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .status { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px; 
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { 
            padding: 10px 20px; 
            margin: 5px; 
            font-size: 16px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
        }
        .record-btn { background-color: #dc3545; color: white; }
        .stop-btn { background-color: #28a745; color: white; }
        .connect-btn { background-color: #007bff; color: white; }
        #output { 
            border: 1px solid #ccc; 
            padding: 10px; 
            height: 300px; 
            overflow-y: auto; 
            white-space: pre-wrap; 
            font-family: monospace; 
        }
    </style>
</head>
<body>
    <h1>🎤 WhisperSync v2.0 音频测试</h1>
    
    <div class="controls">
        <button id="connectBtn" class="connect-btn">连接 WebSocket</button>
        <button id="recordBtn" class="record-btn" disabled>开始录音</button>
        <button id="stopBtn" class="stop-btn" disabled>停止录音</button>
        <button id="clearBtn">清空日志</button>
    </div>
    
    <div id="status" class="status info">准备连接到 ws://localhost:8889/ws</div>
    
    <h3>📋 实时日志</h3>
    <div id="output"></div>
    
    <h3>🔊 转录结果</h3>
    <div id="transcription" style="border: 1px solid #ddd; padding: 10px; min-height: 100px;"></div>

    <script>
        let websocket = null;
        let mediaRecorder = null;
        let audioStream = null;
        let isRecording = false;

        const connectBtn = document.getElementById('connectBtn');
        const recordBtn = document.getElementById('recordBtn');
        const stopBtn = document.getElementById('stopBtn');
        const clearBtn = document.getElementById('clearBtn');
        const statusDiv = document.getElementById('status');
        const outputDiv = document.getElementById('output');
        const transcriptionDiv = document.getElementById('transcription');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            outputDiv.textContent += logMessage;
            outputDiv.scrollTop = outputDiv.scrollHeight;
            
            // 更新状态
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function connectWebSocket() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                log("WebSocket 已经连接", "info");
                return;
            }

            log("正在连接 WebSocket...", "info");
            websocket = new WebSocket('ws://localhost:8889/ws');

            websocket.onopen = function() {
                log("✅ WebSocket 连接成功！", "success");
                connectBtn.disabled = true;
                recordBtn.disabled = false;
                updateUI();
            };

            websocket.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    log(`📥 收到消息: ${data.status || data.type || 'unknown'}`, "info");

                    // 处理转换后的 WhisperLiveKit 格式（优先处理）
                    if (data.type === 'WHISPERLIVEKIT_RESULT') {
                        // 处理确认的段落
                        if (data.confirmed_segments && data.confirmed_segments.length > 0) {
                            data.confirmed_segments.forEach(segment => {
                                if (segment.text && segment.text.trim()) {
                                    const speakerLabel = segment.speaker > 0 ? `说话人 ${segment.speaker}` : '未知说话人';
                                    const confidence = segment.confidence ? ` (置信度: ${(segment.confidence * 100).toFixed(1)}%)` : '';
                                    transcriptionDiv.innerHTML += `<p><strong>${speakerLabel}${confidence}:</strong> ${segment.text}</p>`;
                                    log(`✅ 确认转录 [说话人${segment.speaker}]: ${segment.text}`, "success");
                                }
                            });
                        }

                        // 处理当前实时段落
                        if (data.current_segment && data.current_segment.text && data.current_segment.text.trim()) {
                            const bufferDiv = document.getElementById('buffer-text') || (() => {
                                const div = document.createElement('div');
                                div.id = 'buffer-text';
                                div.style.cssText = 'color: #888; font-style: italic; padding: 5px; border-left: 3px solid #ccc; margin: 5px 0;';
                                transcriptionDiv.appendChild(div);
                                return div;
                            })();
                            const segment = data.current_segment;
                            const speakerLabel = segment.speaker > 0 ? `说话人 ${segment.speaker}` : '未知说话人';
                            const confidence = segment.confidence ? ` (${(segment.confidence * 100).toFixed(1)}%)` : '';
                            bufferDiv.innerHTML = `⏳ 实时识别 [${speakerLabel}${confidence}]: ${segment.text}`;
                            log(`⏳ 实时缓冲 [说话人${segment.speaker}]: ${segment.text}`, "info");
                        }

                        // 显示剩余时间信息
                        if (data.remaining_time_transcription > 0) {
                            log(`⏱️ 转录剩余时间: ${data.remaining_time_transcription.toFixed(1)}秒`, "info");
                        }
                        if (data.remaining_time_diarization > 0) {
                            log(`⏱️ 说话人分析剩余时间: ${data.remaining_time_diarization.toFixed(1)}秒`, "info");
                        }
                    }

                    // 处理 WhisperLiveKit 官方格式（向后兼容）
                    else if (data.status === 'active_transcription') {
                        // 处理确认的转录结果
                        if (data.lines && data.lines.length > 0) {
                            data.lines.forEach(line => {
                                if (line.text && line.text.trim()) {
                                    const speakerLabel = line.speaker > 0 ? `说话人 ${line.speaker}` : '未知说话人';
                                    transcriptionDiv.innerHTML += `<p><strong>${speakerLabel} (${line.beg}-${line.end}):</strong> ${line.text}</p>`;
                                    log(`🗣️ 转录: ${line.text}`, "success");
                                }
                            });
                        }
                        
                        // 处理实时缓冲区内容
                        if (data.buffer_transcription && data.buffer_transcription.trim()) {
                            const bufferDiv = document.getElementById('buffer-text') || (() => {
                                const div = document.createElement('div');
                                div.id = 'buffer-text';
                                div.style.cssText = 'color: #888; font-style: italic; padding: 5px; border-left: 3px solid #ccc; margin: 5px 0;';
                                transcriptionDiv.appendChild(div);
                                return div;
                            })();
                            bufferDiv.innerHTML = `⏳ 实时识别: ${data.buffer_transcription}`;
                            log(`⏳ 实时缓冲: ${data.buffer_transcription}`, "info");
                        }
                        
                        // 显示剩余时间信息
                        if (data.remaining_time_transcription > 0) {
                            log(`⏱️ 转录剩余时间: ${data.remaining_time_transcription.toFixed(1)}秒`, "info");
                        }
                    } 
                    
                    else if (data.status === 'no_audio_detected') {
                        log(`🔇 未检测到音频`, "info");
                    }
                    
                    // 向下兼容原有格式
                    else if (data.type === 'CONNECTION_SUCCESS') {
                        log(`🎉 服务器连接确认: ${data.data.message}`, "success");
                    } else if (data.type === 'WHISPER_RAW_OUTPUT') {
                        if (data.data.lines && data.data.lines.length > 0) {
                            data.data.lines.forEach(line => {
                                if (line.text && line.text.trim()) {
                                    transcriptionDiv.innerHTML += `<p><strong>说话人 ${line.speaker || 1}:</strong> ${line.text}</p>`;
                                    log(`🗣️ 转录: ${line.text}`, "success");
                                }
                            });
                        }
                        if (data.data.text) {
                            transcriptionDiv.innerHTML += `<p>${data.data.text}</p>`;
                            log(`📝 文本: ${data.data.text}`, "success");
                        }
                    } else if (data.type === 'ERROR') {
                        log(`❌ 错误: ${data.message}`, "error");
                    } else {
                        log(`📋 其他消息: ${JSON.stringify(data)}`, "info");
                    }
                } catch (e) {
                    log(`❌ 解析消息失败: ${e.message}`, "error");
                    log(`原始数据: ${event.data}`, "error");
                }
            };

            websocket.onerror = function(error) {
                log(`❌ WebSocket 错误: ${error}`, "error");
            };

            websocket.onclose = function() {
                log("🔌 WebSocket 连接已关闭", "info");
                connectBtn.disabled = false;
                recordBtn.disabled = true;
                stopBtn.disabled = true;
                updateUI();
            };
        }

        async function startRecording() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                log("❌ WebSocket 未连接", "error");
                return;
            }

            try {
                log("🎤 请求麦克风权限...", "info");
                audioStream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    } 
                });

                log("✅ 麦克风权限获取成功", "success");

                // 创建 MediaRecorder
                mediaRecorder = new MediaRecorder(audioStream, { 
                    mimeType: 'audio/webm;codecs=opus'
                });

                mediaRecorder.ondataavailable = function(event) {
                    if (event.data.size > 0 && websocket && websocket.readyState === WebSocket.OPEN) {
                        websocket.send(event.data);
                        log(`📤 发送音频数据: ${event.data.size} 字节`, "info");
                    }
                };

                mediaRecorder.onstart = function() {
                    log("🔴 录音开始", "success");
                    isRecording = true;
                    updateUI();
                };

                mediaRecorder.onstop = function() {
                    log("⏹️ 录音停止", "info");
                    isRecording = false;
                    updateUI();
                };

                // 开始录音，每 500ms 发送一次数据
                mediaRecorder.start(500);

            } catch (error) {
                log(`❌ 录音启动失败: ${error.message}`, "error");
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                
                // 发送 JSON 停止信号而不是空 Blob
                if (websocket && websocket.readyState === WebSocket.OPEN) {
                    const stopMessage = JSON.stringify({ type: 'STOP_RECORDING' });
                    websocket.send(stopMessage);
                    log("📤 发送 JSON 停止信号", "info");
                }
            }

            if (audioStream) {
                audioStream.getTracks().forEach(track => track.stop());
                audioStream = null;
            }
        }

        function updateUI() {
            recordBtn.disabled = !websocket || websocket.readyState !== WebSocket.OPEN || isRecording;
            stopBtn.disabled = !isRecording;
        }

        function clearOutput() {
            outputDiv.textContent = '';
            transcriptionDiv.innerHTML = '';
        }

        // 事件监听器
        connectBtn.addEventListener('click', connectWebSocket);
        recordBtn.addEventListener('click', startRecording);
        stopBtn.addEventListener('click', stopRecording);
        clearBtn.addEventListener('click', clearOutput);

        // 页面加载完成后的初始化
        log("🚀 WhisperSync v2.0 音频测试页面已加载", "info");
        log("请点击'连接 WebSocket'开始测试", "info");
    </script>
</body>
</html> 