#!/usr/bin/env python3
"""
Lightning Whisper MLX 安装和测试脚本
用于验证 lightning-whisper-mlx 在当前系统上的可用性和性能
"""

import subprocess
import sys
import platform
import time
import tempfile
import os
from pathlib import Path

def run_command(command, check=True):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            check=check
        )
        return result.stdout.strip(), result.stderr.strip(), result.returncode
    except subprocess.CalledProcessError as e:
        return e.stdout, e.stderr, e.returncode

def check_system_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查操作系统
    system = platform.system()
    machine = platform.machine()
    print(f"操作系统: {system} {machine}")
    
    if system != "Darwin":
        print("❌ Lightning Whisper MLX 需要 macOS 系统")
        return False
    
    if machine != "arm64":
        print("⚠️ 建议使用 Apple Silicon (M1/M2/M3) 以获得最佳性能")
    
    # 检查 Python 版本
    python_version = sys.version_info
    print(f"Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        return False
    
    print("✅ 系统要求检查通过")
    return True

def install_dependencies():
    """安装依赖"""
    print("\n📦 安装依赖...")
    
    dependencies = [
        "mlx",
        "lightning-whisper-mlx", 
        "soundfile",
        "numpy",
        "fastapi",
        "uvicorn[standard]",
        "websockets"
    ]
    
    for dep in dependencies:
        print(f"安装 {dep}...")
        stdout, stderr, code = run_command(f"{sys.executable} -m pip install {dep}")
        
        if code != 0:
            print(f"❌ 安装 {dep} 失败:")
            print(f"错误: {stderr}")
            return False
        else:
            print(f"✅ {dep} 安装成功")
    
    return True

def test_lightning_whisper_mlx():
    """测试 Lightning Whisper MLX"""
    print("\n🧪 测试 Lightning Whisper MLX...")
    
    try:
        # 导入测试
        print("导入 lightning-whisper-mlx...")
        from lightning_whisper_mlx import LightningWhisperMLX
        print("✅ 导入成功")
        
        # 创建实例测试
        print("创建 LightningWhisperMLX 实例...")
        whisper = LightningWhisperMLX(
            model="tiny",  # 使用最小模型进行测试
            batch_size=4,
            quant=None
        )
        print("✅ 实例创建成功")
        
        # 创建测试音频文件
        print("创建测试音频...")
        import numpy as np
        import soundfile as sf
        
        # 生成 3 秒的测试音频（440Hz 正弦波）
        sample_rate = 16000
        duration = 3.0
        t = np.linspace(0, duration, int(sample_rate * duration))
        audio = 0.3 * np.sin(2 * np.pi * 440 * t)  # 440Hz A音
        
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            sf.write(temp_file.name, audio, sample_rate)
            test_audio_path = temp_file.name
        
        # 转录测试
        print("执行转录测试...")
        start_time = time.time()
        
        result = whisper.transcribe(test_audio_path)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"✅ 转录完成，耗时: {processing_time:.2f} 秒")
        print(f"转录结果: {result.get('text', 'N/A')}")
        print(f"检测语言: {result.get('language', 'N/A')}")
        
        # 清理测试文件
        os.unlink(test_audio_path)
        
        # 性能评估
        audio_duration = duration
        rtf = processing_time / audio_duration
        print(f"实时因子 (RTF): {rtf:.3f}")
        
        if rtf < 1.0:
            print("🚀 实时处理能力: 优秀")
        elif rtf < 2.0:
            print("⚡ 实时处理能力: 良好") 
        else:
            print("⚠️ 实时处理能力: 需要优化")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_different_models():
    """测试不同模型的性能"""
    print("\n🎯 测试不同模型性能...")
    
    try:
        from lightning_whisper_mlx import LightningWhisperMLX
        import numpy as np
        import soundfile as sf
        
        # 创建测试音频
        sample_rate = 16000
        duration = 5.0  # 5秒测试音频
        t = np.linspace(0, duration, int(sample_rate * duration))
        # 创建更复杂的测试音频（多频率混合）
        audio = 0.2 * (np.sin(2 * np.pi * 440 * t) + 
                      0.15 * np.sin(2 * np.pi * 880 * t) +
                      0.1 * np.sin(2 * np.pi * 1320 * t))
        
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            sf.write(temp_file.name, audio, sample_rate)
            test_audio_path = temp_file.name
        
        # 测试不同模型
        models_to_test = [
            ("tiny", None),
            ("tiny", "4bit"),
            ("small", None),
            ("small", "4bit"),
        ]
        
        results = []
        
        for model_name, quant in models_to_test:
            try:
                print(f"\n测试模型: {model_name}" + (f" ({quant})" if quant else ""))
                
                whisper = LightningWhisperMLX(
                    model=model_name,
                    batch_size=8,
                    quant=quant
                )
                
                start_time = time.time()
                result = whisper.transcribe(test_audio_path)
                end_time = time.time()
                
                processing_time = end_time - start_time
                rtf = processing_time / duration
                
                results.append({
                    "model": model_name,
                    "quant": quant,
                    "time": processing_time,
                    "rtf": rtf,
                    "text": result.get("text", "")
                })
                
                print(f"  ⏱️ 处理时间: {processing_time:.2f}s")
                print(f"  📊 RTF: {rtf:.3f}")
                print(f"  📝 结果: {result.get('text', 'N/A')[:50]}...")
                
            except Exception as e:
                print(f"  ❌ 模型 {model_name} 测试失败: {e}")
        
        # 清理测试文件
        os.unlink(test_audio_path)
        
        # 性能总结
        print("\n📊 性能总结:")
        print("模型\t\t量化\t处理时间\tRTF")
        print("-" * 50)
        for r in results:
            quant_str = r["quant"] or "无"
            print(f"{r['model']}\t\t{quant_str}\t{r['time']:.2f}s\t\t{r['rtf']:.3f}")
        
        # 推荐配置
        if results:
            best_rtf = min(results, key=lambda x: x["rtf"])
            print(f"\n🏆 最佳性能配置: {best_rtf['model']}" + 
                  (f" ({best_rtf['quant']})" if best_rtf['quant'] else ""))
            print(f"   RTF: {best_rtf['rtf']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False

def generate_config_recommendation():
    """生成配置建议"""
    print("\n💡 配置建议:")
    
    system = platform.system()
    machine = platform.machine()
    
    if system == "Darwin" and machine == "arm64":
        print("🍎 Apple Silicon 优化配置:")
        print("  推荐模型: distil-small.en (英语) 或 small (多语言)")
        print("  推荐量化: 4bit (内存优化) 或 8bit (质量平衡)")
        print("  推荐批量大小: 12-16")
        print("  预期 RTF: < 0.1 (实时处理)")
    else:
        print("⚠️ 非 Apple Silicon 系统:")
        print("  性能可能不如预期")
        print("  建议使用较小模型: tiny 或 small")
        print("  建议启用量化: 4bit")
    
    print("\n🚀 生产环境建议:")
    print("  - 使用 distil-small.en 获得最佳速度")
    print("  - 启用 4bit 量化减少内存占用")
    print("  - 批量大小根据内存调整 (8-16)")
    print("  - 音频块大小: 2-3 秒")
    print("  - 重叠率: 50%")

def main():
    """主函数"""
    print("🚀 Lightning Whisper MLX 安装和测试")
    print("=" * 50)
    
    # 检查系统要求
    if not check_system_requirements():
        print("\n❌ 系统要求不满足，退出")
        return False
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 依赖安装失败，退出")
        return False
    
    # 基础测试
    if not test_lightning_whisper_mlx():
        print("\n❌ Lightning Whisper MLX 测试失败")
        return False
    
    # 性能测试
    test_different_models()
    
    # 生成建议
    generate_config_recommendation()
    
    print("\n✅ 所有测试完成！")
    print("🎯 Lightning Whisper MLX 已准备就绪，可以开始开发流式服务")
    
    return True

def setup_cross_platform_service():
    """设置跨平台流式服务"""
    print("\n🔧 设置跨平台流式服务...")

    # 安装通用依赖
    common_deps = [
        "fastapi",
        "uvicorn[standard]",
        "websockets",
        "numpy",
        "soundfile",
        "scipy"  # 用于 VAD 滤波
    ]

    for dep in common_deps:
        print(f"安装 {dep}...")
        stdout, stderr, code = run_command(f"{sys.executable} -m pip install {dep}")
        if code != 0:
            print(f"❌ 安装 {dep} 失败: {stderr}")
            return False
        else:
            print(f"✅ {dep} 安装成功")

    # 根据平台安装特定后端
    system = platform.system()
    machine = platform.machine()

    if system == "Darwin" and machine == "arm64":
        print("🍎 Apple Silicon 检测到，安装 Lightning Whisper MLX...")
        stdout, stderr, code = run_command(f"{sys.executable} -m pip install lightning-whisper-mlx")
        if code == 0:
            print("✅ Lightning Whisper MLX 安装成功")
        else:
            print(f"⚠️ Lightning Whisper MLX 安装失败，将使用 faster-whisper: {stderr}")

    # 安装 faster-whisper 作为通用后端
    print("安装 faster-whisper...")
    stdout, stderr, code = run_command(f"{sys.executable} -m pip install faster-whisper")
    if code == 0:
        print("✅ faster-whisper 安装成功")
    else:
        print(f"⚠️ faster-whisper 安装失败: {stderr}")
        # 安装 openai-whisper 作为最后备选
        print("安装 openai-whisper 作为备选...")
        stdout, stderr, code = run_command(f"{sys.executable} -m pip install openai-whisper")
        if code == 0:
            print("✅ openai-whisper 安装成功")
        else:
            print(f"❌ 所有 Whisper 后端安装失败")
            return False

    return True

def test_cross_platform_service():
    """测试跨平台服务"""
    print("\n🧪 测试跨平台流式服务...")

    try:
        # 导入测试
        print("导入跨平台服务...")
        import sys
        import os

        # 添加当前目录到路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)

        from whisper_streaming_service import WhisperStreamingService, StreamingConfig
        print("✅ 服务导入成功")

        # 创建服务实例
        print("创建服务实例...")
        config = StreamingConfig(
            step_ms=3000,
            length_ms=10000,
            keep_ms=200,
            use_vad=False
        )
        service = WhisperStreamingService(config)
        print("✅ 服务实例创建成功")

        # 测试后端检测
        backend = service.whisper.backend_type
        print(f"✅ 检测到后端: {backend}")

        # 创建测试音频
        print("创建测试音频...")
        import numpy as np
        sample_rate = 16000
        duration = 3.0
        t = np.linspace(0, duration, int(sample_rate * duration))
        audio = 0.3 * np.sin(2 * np.pi * 440 * t)  # 440Hz A音

        # 测试转录
        print("执行转录测试...")
        import asyncio

        async def test_transcribe():
            result = await service.whisper.transcribe_async(audio, "auto")
            return result

        result = asyncio.run(test_transcribe())

        print(f"✅ 转录测试完成")
        print(f"后端: {backend}")
        print(f"结果: {result.get('text', 'N/A')}")
        print(f"语言: {result.get('language', 'N/A')}")

        return True

    except Exception as e:
        print(f"❌ 跨平台服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_startup_script():
    """创建启动脚本"""
    print("\n📝 创建启动脚本...")

    startup_script = '''#!/usr/bin/env python3
"""
跨平台 Whisper 流式服务启动脚本
"""

import subprocess
import sys
import os

def main():
    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    service_path = os.path.join(script_dir, "whisper_streaming_service.py")

    if not os.path.exists(service_path):
        print(f"❌ 服务文件不存在: {service_path}")
        return 1

    print("🚀 启动跨平台 Whisper 流式服务...")
    print("服务地址: http://localhost:8891")
    print("WebSocket: ws://localhost:8891/ws")
    print("健康检查: http://localhost:8891/health")
    print("按 Ctrl+C 停止服务")
    print("-" * 50)

    try:
        subprocess.run([sys.executable, service_path], check=True)
    except KeyboardInterrupt:
        print("\\n👋 服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 服务启动失败: {e}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
'''

    with open("start_whisper_streaming.py", "w", encoding="utf-8") as f:
        f.write(startup_script)

    # 设置执行权限
    import stat
    os.chmod("start_whisper_streaming.py", stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)

    print("✅ 启动脚本已创建: start_whisper_streaming.py")

def main():
    """主函数"""
    print("🚀 跨平台 Whisper 流式服务安装和测试")
    print("=" * 60)

    # 检查系统要求
    if not check_system_requirements():
        print("\n❌ 系统要求不满足，退出")
        return False

    # 设置跨平台服务
    if not setup_cross_platform_service():
        print("\n❌ 跨平台服务设置失败，退出")
        return False

    # 测试服务
    if not test_cross_platform_service():
        print("\n❌ 跨平台服务测试失败")
        return False

    # 创建启动脚本
    create_startup_script()

    # 生成使用说明
    print("\n🎯 安装完成！使用说明:")
    print("1. 启动服务: python start_whisper_streaming.py")
    print("2. 测试连接: curl http://localhost:8891/health")
    print("3. WebSocket: ws://localhost:8891/ws")
    print("4. 前端可以直接连接到新的端口 8891")

    print("\n✅ 所有设置完成！")
    print("🎯 跨平台 Whisper 流式服务已准备就绪")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
