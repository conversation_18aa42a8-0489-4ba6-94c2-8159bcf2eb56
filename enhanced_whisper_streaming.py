#!/usr/bin/env python3
"""
增强版跨平台流式语音识别服务
集成 Silero VAD、智能句子分割、部分更新管理
"""

import asyncio
import json
import logging
import numpy as np
import time
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass, field
from difflib import SequenceMatcher
import tempfile
import os

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# 导入基础组件
from whisper_streaming_service import (
    StreamingConfig, SlidingWindowBuffer, CrossPlatformWhisper
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class EnhancedConfig(StreamingConfig):
    """增强配置"""
    use_silero_vad: bool = True
    vad_threshold: float = 0.5
    min_speech_duration_ms: int = 250
    min_silence_duration_ms: int = 100
    debug_skip_vad: bool = False  # 调试模式：跳过 VAD 检测
    sentence_confidence_threshold: float = 0.7
    update_similarity_threshold: float = 0.8
    enable_speaker_diarization: bool = True  # 启用 Resemblyzer 说话人分离

class SileroVAD:
    """Silero VAD 语音活动检测"""
    
    def __init__(self, threshold: float = 0.5):
        self.threshold = threshold
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """加载 Silero VAD 模型"""
        try:
            import torch
            self.model, self.utils = torch.hub.load(
                repo_or_dir='snakers4/silero-vad',
                model='silero_vad',
                force_reload=False,
                onnx=False
            )
            self.get_speech_timestamps = self.utils[0]
            self.model.eval()
            logger.info("✅ Silero VAD 模型加载成功")
        except Exception as e:
            logger.error(f"❌ Silero VAD 加载失败: {e}")
            self.model = None
    
    def detect_speech_segments(self, audio: np.ndarray, sample_rate: int = 16000) -> List[Dict]:
        """检测语音段落"""
        if self.model is None:
            # 降级到简单 VAD
            return self._simple_vad_fallback(audio, sample_rate)
        
        try:
            import torch
            
            # 转换为 torch tensor
            audio_tensor = torch.from_numpy(audio).float()
            
            # 检测语音时间戳 - 使用更宽松的参数
            speech_timestamps = self.get_speech_timestamps(
                audio_tensor,
                self.model,
                sampling_rate=sample_rate,
                threshold=self.threshold,
                min_speech_duration_ms=100,  # 减少最小语音持续时间
                min_silence_duration_ms=50   # 减少最小静音持续时间
            )
            
            # 转换格式
            segments = []
            for segment in speech_timestamps:
                segments.append({
                    "start": segment['start'] / sample_rate,
                    "end": segment['end'] / sample_rate,
                    "confidence": 0.9  # Silero VAD 通常很准确
                })
            
            return segments
            
        except Exception as e:
            logger.error(f"Silero VAD 检测失败: {e}")
            return self._simple_vad_fallback(audio, sample_rate)
    
    def _simple_vad_fallback(self, audio: np.ndarray, sample_rate: int) -> List[Dict]:
        """简单 VAD 备选方案"""
        energy = np.mean(np.abs(audio))
        logger.debug(f"简单 VAD: 音频能量 = {energy:.6f}")

        # 降低阈值，更容易检测到语音
        if energy > 0.005:  # 降低阈值
            return [{
                "start": 0.0,
                "end": len(audio) / sample_rate,
                "confidence": 0.6
            }]
        return []

@dataclass
class SentenceSegment:
    """句子段落"""
    text: str
    start_time: float
    end_time: float
    confidence: float
    is_final: bool = False
    speaker_id: Optional[int] = None

class IntelligentSegmentation:
    """智能句子分割器"""
    
    def __init__(self, confidence_threshold: float = 0.7):
        self.confidence_threshold = confidence_threshold
        
        # 句子结束标记
        self.sentence_endings = {'.', '!', '?', '。', '！', '？', '…'}
        self.pause_indicators = {',', '，', ';', '；', ':', '：'}
        
        # 状态管理
        self.accumulated_text = ""
        self.confirmed_sentences: List[SentenceSegment] = []
        self.pending_segment: Optional[SentenceSegment] = None
        self.last_update_time = time.time()
        
    def process_transcription(self, 
                            new_text: str, 
                            confidence: float,
                            timestamp: float) -> Dict[str, Any]:
        """处理新的转录结果"""
        
        # 1. 检测文本变化
        change_analysis = self._analyze_text_changes(new_text)
        
        # 2. 更新累积文本
        self._update_accumulated_text(new_text, change_analysis)
        
        # 3. 句子分割
        new_sentences = self._segment_sentences(confidence, timestamp)
        
        # 4. 管理待确认文本
        pending_text = self._update_pending_text(confidence, timestamp)
        
        return {
            "confirmed_sentences": new_sentences,
            "pending_text": pending_text,
            "change_analysis": change_analysis,
            "total_sentences": len(self.confirmed_sentences)
        }
    
    def _analyze_text_changes(self, new_text: str) -> Dict[str, Any]:
        """分析文本变化"""
        if not self.accumulated_text:
            return {
                "type": "initial",
                "similarity": 1.0,
                "changes": []
            }
        
        # 使用序列匹配器分析差异
        matcher = SequenceMatcher(None, self.accumulated_text, new_text)
        similarity = matcher.ratio()
        
        # 获取操作码
        opcodes = matcher.get_opcodes()
        changes = []
        
        for tag, i1, i2, j1, j2 in opcodes:
            if tag != 'equal':
                changes.append({
                    "operation": tag,
                    "old_text": self.accumulated_text[i1:i2] if tag != 'insert' else "",
                    "new_text": new_text[j1:j2] if tag != 'delete' else "",
                    "position": i1
                })
        
        # 判断变化类型
        if similarity > 0.9:
            change_type = "append"
        elif similarity > 0.7:
            change_type = "partial_update"
        else:
            change_type = "major_revision"
        
        return {
            "type": change_type,
            "similarity": similarity,
            "changes": changes
        }
    
    def _update_accumulated_text(self, new_text: str, change_analysis: Dict):
        """更新累积文本"""
        if change_analysis["type"] == "initial":
            self.accumulated_text = new_text
        elif change_analysis["type"] == "append":
            # 简单追加
            self.accumulated_text = new_text
        elif change_analysis["similarity"] > 0.5:
            # 部分更新
            self.accumulated_text = new_text
        else:
            # 主要修订，谨慎处理
            logger.warning(f"主要文本修订检测到，相似度: {change_analysis['similarity']:.2f}")
            self.accumulated_text = new_text
    
    def _segment_sentences(self, confidence: float, timestamp: float) -> List[SentenceSegment]:
        """分割句子"""
        new_sentences = []
        
        # 基于标点符号分割
        sentences = self._split_by_punctuation(self.accumulated_text)
        
        for i, sentence_text in enumerate(sentences[:-1]):  # 排除最后一个未完成的句子
            if sentence_text.strip():
                # 检查是否已经确认过这个句子
                if not self._is_sentence_confirmed(sentence_text):
                    sentence = SentenceSegment(
                        text=sentence_text.strip(),
                        start_time=timestamp - 3.0,  # 估算开始时间
                        end_time=timestamp,
                        confidence=confidence,
                        is_final=True
                    )
                    self.confirmed_sentences.append(sentence)
                    new_sentences.append(sentence)
        
        return new_sentences
    
    def _split_by_punctuation(self, text: str) -> List[str]:
        """基于标点符号分割文本"""
        sentences = []
        current_sentence = ""
        
        for char in text:
            current_sentence += char
            if char in self.sentence_endings:
                sentences.append(current_sentence)
                current_sentence = ""
        
        # 添加未完成的句子
        if current_sentence.strip():
            sentences.append(current_sentence)
        
        return sentences
    
    def _is_sentence_confirmed(self, sentence_text: str) -> bool:
        """检查句子是否已经确认"""
        for confirmed in self.confirmed_sentences:
            if confirmed.text == sentence_text.strip():
                return True
        return False
    
    def _update_pending_text(self, confidence: float, timestamp: float) -> Optional[SentenceSegment]:
        """更新待确认文本"""
        # 获取最后一个未完成的句子
        sentences = self._split_by_punctuation(self.accumulated_text)
        if sentences and not sentences[-1].endswith(tuple(self.sentence_endings)):
            pending_text = sentences[-1].strip()
            if pending_text:
                self.pending_segment = SentenceSegment(
                    text=pending_text,
                    start_time=timestamp - 2.0,
                    end_time=timestamp,
                    confidence=confidence,
                    is_final=False
                )
                return self.pending_segment
        
        self.pending_segment = None
        return None

class PartialUpdateManager:
    """部分更新管理器"""
    
    def __init__(self, similarity_threshold: float = 0.8):
        self.similarity_threshold = similarity_threshold
        self.update_history: List[Dict] = []
        self.last_confirmed_state = ""
        
    def should_update(self, new_text: str, confidence: float) -> bool:
        """判断是否应该更新"""
        if not self.last_confirmed_state:
            return True
        
        similarity = SequenceMatcher(
            None, self.last_confirmed_state, new_text
        ).ratio()
        
        # 高置信度或显著变化时更新
        return confidence > 0.8 or similarity < self.similarity_threshold
    
    def record_update(self, old_text: str, new_text: str, confidence: float):
        """记录更新"""
        self.update_history.append({
            "timestamp": time.time(),
            "old_text": old_text,
            "new_text": new_text,
            "confidence": confidence,
            "similarity": SequenceMatcher(None, old_text, new_text).ratio()
        })
        
        # 保持历史记录在合理范围内
        if len(self.update_history) > 100:
            self.update_history = self.update_history[-50:]
        
        self.last_confirmed_state = new_text

class ResemblyzerSpeakerDiarization:
    """基于 Resemblyzer 的说话人分离"""

    def __init__(self):
        self.backend = "resemblyzer"
        self.model = None
        self.speaker_embeddings = {}
        self.current_speaker_id = 0

        if self._load_model():
            logger.info("✅ Resemblyzer 说话人分离初始化成功")
        else:
            logger.warning("⚠️ Resemblyzer 不可用，说话人分离功能将被禁用")
            self.backend = None

    def _load_model(self) -> bool:
        """加载 Resemblyzer 模型"""
        try:
            from resemblyzer import VoiceEncoder
            self.model = VoiceEncoder()
            logger.info("✅ Resemblyzer 模型加载成功")
            return True
        except ImportError:
            logger.warning("⚠️ Resemblyzer 未安装")
            return False
        except Exception as e:
            logger.error(f"❌ Resemblyzer 模型加载失败: {e}")
            return False

    def process_audio_chunk(self, audio: np.ndarray, timestamp: float) -> Dict[str, Any]:
        """处理音频块进行说话人分离"""
        if not self.model or not self.backend:
            return {
                "speakers": [],
                "speaker_changes": False,
                "confidence": 0.0
            }

        try:
            if self.backend == "resemblyzer" and self.model:
                return self._process_with_resemblyzer(audio, timestamp)
            else:
                return {
                    "speakers": [],
                    "speaker_changes": False,
                    "confidence": 0.0
                }
        except Exception as e:
            logger.error(f"Resemblyzer 处理失败: {e}")
            return {
                "speakers": [],
                "speaker_changes": False,
                "confidence": 0.0
            }

    def _process_with_resemblyzer(self, audio: np.ndarray, timestamp: float) -> Dict[str, Any]:
        """使用 Resemblyzer 处理"""
        # 简化的说话人识别逻辑
        embedding = self.model.embed_utterance(audio)

        # 简单的说话人匹配
        speaker_id = self._match_speaker(embedding)

        return {
            "speakers": [speaker_id],
            "speaker_changes": speaker_id != self.current_speaker_id,
            "confidence": 0.8
        }





    def _match_speaker(self, embedding) -> int:
        """匹配说话人"""
        if not self.speaker_embeddings:
            # 第一个说话人
            self.speaker_embeddings[0] = embedding
            self.current_speaker_id = 0
            return 0

        # 计算与已知说话人的相似度
        max_similarity = 0
        best_speaker = 0

        for speaker_id, known_embedding in self.speaker_embeddings.items():
            similarity = self._calculate_similarity(embedding, known_embedding)
            if similarity > max_similarity:
                max_similarity = similarity
                best_speaker = speaker_id

        # 如果相似度太低，认为是新说话人
        if max_similarity < 0.6:
            new_speaker_id = len(self.speaker_embeddings)
            self.speaker_embeddings[new_speaker_id] = embedding
            self.current_speaker_id = new_speaker_id
            return new_speaker_id
        else:
            self.current_speaker_id = best_speaker
            return best_speaker

    def _calculate_similarity(self, emb1, emb2) -> float:
        """计算嵌入相似度"""
        try:
            import numpy as np
            # 余弦相似度
            dot_product = np.dot(emb1, emb2)
            norm1 = np.linalg.norm(emb1)
            norm2 = np.linalg.norm(emb2)
            return dot_product / (norm1 * norm2)
        except:
            return 0.5  # 默认中等相似度

class EnhancedStreamingService:
    """增强版流式语音识别服务"""
    
    def __init__(self, config: EnhancedConfig = None):
        self.config = config or EnhancedConfig()

        # 核心组件
        self.whisper = CrossPlatformWhisper()
        self.vad = SileroVAD(self.config.vad_threshold) if self.config.use_silero_vad else None
        self.segmentation = IntelligentSegmentation(self.config.sentence_confidence_threshold)
        self.update_manager = PartialUpdateManager(self.config.update_similarity_threshold)

        # 说话人分离组件
        self.diarization = None
        if self.config.enable_speaker_diarization:
            self.diarization = ResemblyzerSpeakerDiarization()

        logger.info("🚀 增强版流式服务初始化完成")
        logger.info(f"VAD: {'Silero' if self.vad else '禁用'}")
        logger.info(f"说话人分离: {self.diarization.backend if self.diarization else '禁用'}")
        logger.info(f"后端: {self.whisper.backend_type}")
    
    async def process_audio_stream(self, websocket: WebSocket):
        """处理音频流"""
        buffer = SlidingWindowBuffer(self.config)
        session_start_time = time.time()
        
        try:
            while True:
                message = await websocket.receive()
                
                if message.get("type") == "websocket.receive" and "bytes" in message:
                    audio_data = message["bytes"]
                    
                    if len(audio_data) == 0:
                        # 结束处理
                        await self._send_final_result(websocket)
                        break
                    
                    # 转换音频数据
                    audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0

                    # 调试信息：音频数据统计
                    audio_max = np.max(np.abs(audio_array))
                    audio_mean = np.mean(np.abs(audio_array))
                    logger.debug(f"音频数据: 长度={len(audio_array)}, 最大值={audio_max:.4f}, 平均值={audio_mean:.4f}")

                    # 添加到缓冲区
                    window = buffer.add_audio(audio_array)
                    
                    if window is not None:
                        await self._process_audio_window(websocket, window, time.time())
                
                elif message.get("type") == "websocket.receive" and "text" in message:
                    try:
                        control_msg = json.loads(message["text"])
                        if control_msg.get("type") == "STOP_RECORDING":
                            await self._send_final_result(websocket)
                            break
                    except json.JSONDecodeError:
                        pass
                        
        except WebSocketDisconnect:
            logger.info("WebSocket 连接断开")
        except Exception as e:
            logger.error(f"音频流处理错误: {e}")
            await websocket.send_json({
                "type": "ERROR",
                "message": f"处理错误: {str(e)}"
            })
    
    async def _process_audio_window(self, websocket: WebSocket, audio: np.ndarray, timestamp: float):
        """处理音频窗口"""
        try:
            # 1. VAD 检测
            if self.vad and not self.config.debug_skip_vad:
                speech_segments = self.vad.detect_speech_segments(audio, self.config.sample_rate)
                logger.info(f"VAD 检测结果: {len(speech_segments)} 个语音段落")

                if not speech_segments:
                    # 添加音频能量信息
                    audio_energy = np.mean(np.abs(audio))
                    logger.info(f"未检测到语音，音频能量: {audio_energy:.6f}")

                    await websocket.send_json({
                        "type": "NO_SPEECH_DETECTED",
                        "timestamp": timestamp,
                        "audio_energy": float(audio_energy),
                        "vad_threshold": self.config.vad_threshold
                    })
                    return
                else:
                    logger.info(f"检测到语音段落: {speech_segments}")
            elif self.config.debug_skip_vad:
                logger.info("调试模式：跳过 VAD 检测")
            
            # 2. 语音识别
            transcription_result = await self.whisper.transcribe_async(audio, self.config.language)
            
            if not transcription_result.get("text", "").strip():
                return
            
            # 3. 说话人分离（如果启用）
            speaker_info = None
            if self.diarization:
                speaker_info = self.diarization.process_audio_chunk(audio, timestamp)

            # 4. 智能分割和更新管理
            segmentation_result = self.segmentation.process_transcription(
                transcription_result["text"],
                transcription_result.get("confidence", 0.8),
                timestamp
            )

            # 5. 如果有说话人信息，更新句子的说话人标签
            if speaker_info and speaker_info.get("speakers"):
                for sentence in segmentation_result["confirmed_sentences"]:
                    sentence.speaker_id = speaker_info["speakers"][0]  # 使用第一个检测到的说话人

            # 6. 发送增强结果
            await self._send_enhanced_result(websocket, {
                "transcription": transcription_result,
                "segmentation": segmentation_result,
                "speaker_info": speaker_info,
                "timestamp": timestamp,
                "backend": self.whisper.backend_type
            })
            
        except Exception as e:
            logger.error(f"音频窗口处理错误: {e}")
            await websocket.send_json({
                "type": "PROCESSING_ERROR",
                "message": str(e),
                "timestamp": timestamp
            })
    
    async def _send_enhanced_result(self, websocket: WebSocket, result: Dict):
        """发送增强结果"""
        # 构建响应消息
        response = {
            "type": "ENHANCED_TRANSCRIPTION_RESULT",
            "timestamp": result["timestamp"],
            "backend": result["backend"],
            
            # 原始转录结果
            "raw_transcription": {
                "text": result["transcription"]["text"],
                "language": result["transcription"].get("language", "unknown"),
                "confidence": result["transcription"].get("confidence", 0.8)
            },
            
            # 分割结果
            "confirmed_sentences": [
                {
                    "text": sentence.text,
                    "start_time": sentence.start_time,
                    "end_time": sentence.end_time,
                    "confidence": sentence.confidence,
                    "is_final": sentence.is_final,
                    "speaker_id": sentence.speaker_id
                }
                for sentence in result["segmentation"]["confirmed_sentences"]
            ],
            
            # 待确认文本
            "pending_text": {
                "text": result["segmentation"]["pending_text"].text if result["segmentation"]["pending_text"] else "",
                "confidence": result["segmentation"]["pending_text"].confidence if result["segmentation"]["pending_text"] else 0.0
            },
            
            # 变化分析
            "change_analysis": result["segmentation"]["change_analysis"],

            # 说话人信息
            "speaker_info": result.get("speaker_info", {
                "speakers": [],
                "speaker_changes": False,
                "confidence": 0.0
            }),

            # 统计信息
            "stats": {
                "total_sentences": result["segmentation"]["total_sentences"],
                "processing_time": time.time() - result["timestamp"]
            }
        }
        
        await websocket.send_json(response)
    
    async def _send_final_result(self, websocket: WebSocket):
        """发送最终结果"""
        final_sentences = self.segmentation.confirmed_sentences
        
        await websocket.send_json({
            "type": "TRANSCRIPTION_COMPLETE",
            "final_sentences": [
                {
                    "text": sentence.text,
                    "start_time": sentence.start_time,
                    "end_time": sentence.end_time,
                    "confidence": sentence.confidence,
                    "speaker_id": sentence.speaker_id
                }
                for sentence in final_sentences
            ],
            "total_sentences": len(final_sentences),
            "session_summary": {
                "duration": time.time() - (final_sentences[0].start_time if final_sentences else 0),
                "average_confidence": np.mean([s.confidence for s in final_sentences]) if final_sentences else 0.0
            }
        })

# FastAPI 应用
app = FastAPI(title="增强版跨平台 Whisper 流式服务")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局服务实例
enhanced_service: Optional[EnhancedStreamingService] = None

@app.on_event("startup")
async def startup_event():
    """启动时初始化服务"""
    global enhanced_service
    try:
        config = EnhancedConfig(
            step_ms=3000,
            length_ms=10000,
            keep_ms=200,
            use_silero_vad=True,
            vad_threshold=0.2,  # 进一步降低 VAD 阈值
            sentence_confidence_threshold=0.7,
            debug_skip_vad=False,  # 恢复 VAD 检测
            language="auto"
        )
        enhanced_service = EnhancedStreamingService(config)
        logger.info("🚀 增强版跨平台 Whisper 流式服务启动成功")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        raise

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "增强版跨平台 Whisper 流式服务",
        "backend": enhanced_service.whisper.backend_type if enhanced_service else "未初始化",
        "features": {
            "silero_vad": enhanced_service.vad is not None if enhanced_service else False,
            "intelligent_segmentation": True,
            "partial_updates": True,
            "speaker_diarization": enhanced_service.diarization is not None if enhanced_service else False,
            "speaker_backend": enhanced_service.diarization.backend if enhanced_service and enhanced_service.diarization else None
        }
    }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket 端点"""
    await websocket.accept()
    logger.info("🔌 WebSocket 连接已建立")
    
    await websocket.send_json({
        "type": "CONNECTION_SUCCESS",
        "message": "增强版跨平台 Whisper 流式服务已连接",
        "backend": enhanced_service.whisper.backend_type,
        "features": {
            "silero_vad": enhanced_service.vad is not None,
            "intelligent_segmentation": True,
            "partial_updates": True,
            "speaker_diarization": enhanced_service.diarization is not None,
            "speaker_backend": enhanced_service.diarization.backend if enhanced_service.diarization else None
        }
    })
    
    await enhanced_service.process_audio_stream(websocket)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8892)
