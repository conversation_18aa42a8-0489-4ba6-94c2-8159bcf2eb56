# Whisper.cpp 流式实现深度分析

## 🔍 核心架构分析

基于对 `whisper.cpp/examples/stream/stream.cpp` 的深入分析，我发现了 whisper.cpp 流式语音识别的核心设计模式。

## 📊 关键技术要点

### 1. 滑动窗口机制 (Sliding Window)

```cpp
// 核心参数
int32_t step_ms    = 3000;   // 步进间隔：3秒
int32_t length_ms  = 10000;  // 处理窗口：10秒  
int32_t keep_ms    = 200;    // 保留重叠：200ms
```

**工作原理**：
- 每 3 秒处理一次音频
- 每次处理 10 秒的音频窗口
- 保留前一次的 200ms 音频避免词边界问题

### 2. 音频缓冲管理

```cpp
// 音频缓冲区
std::vector<float> pcmf32(n_samples_30s, 0.0f);     // 当前处理窗口
std::vector<float> pcmf32_old;                       // 上一次的音频
std::vector<float> pcmf32_new(n_samples_30s, 0.0f); // 新采集的音频

// 音频拼接逻辑
const int n_samples_take = std::min(
    (int) pcmf32_old.size(), 
    std::max(0, n_samples_keep + n_samples_len - n_samples_new)
);

// 拼接：旧音频尾部 + 新音频
for (int i = 0; i < n_samples_take; i++) {
    pcmf32[i] = pcmf32_old[pcmf32_old.size() - n_samples_take + i];
}
memcpy(pcmf32.data() + n_samples_take, pcmf32_new.data(), n_samples_new*sizeof(float));
```

### 3. 两种工作模式

#### 模式 A：固定步进模式 (Fixed Step Mode)
- `step_ms > 0`：每隔固定时间处理
- 适合实时转录
- 使用音频重叠避免断词

#### 模式 B：VAD 触发模式 (VAD Triggered Mode)  
- `step_ms <= 0`：语音活动检测触发
- 检测到语音才开始处理
- 更节省计算资源

### 4. VAD (语音活动检测) 实现

```cpp
bool vad_simple(std::vector<float> & pcmf32, int sample_rate, int last_ms, 
                float vad_thold, float freq_thold, bool verbose) {
    // 1. 高通滤波去除低频噪声
    if (freq_thold > 0.0f) {
        high_pass_filter(pcmf32, freq_thold, sample_rate);
    }
    
    // 2. 计算整体能量和最近能量
    float energy_all = 0.0f;
    float energy_last = 0.0f;
    
    for (int i = 0; i < n_samples; i++) {
        energy_all += fabsf(pcmf32[i]);
        if (i >= n_samples - n_samples_last) {
            energy_last += fabsf(pcmf32[i]);
        }
    }
    
    // 3. 自适应阈值判断
    return energy_last > vad_thold * energy_all;
}
```

### 5. 上下文保持机制

```cpp
// 保持转录上下文
std::vector<whisper_token> prompt_tokens;

// 将上一次的结果作为下一次的提示
if (!params.no_context) {
    prompt_tokens.clear();
    const int n_segments = whisper_full_n_segments(ctx);
    for (int i = 0; i < n_segments; ++i) {
        const int token_count = whisper_full_n_tokens(ctx, i);
        for (int j = 0; j < token_count; ++j) {
            prompt_tokens.push_back(whisper_full_get_token_id(ctx, i, j));
        }
    }
}
```

## 🚀 我们的实现策略

基于 whisper.cpp 的设计，我们可以构建一个跨平台的流式服务：

### 架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  Python Server   │    │  推理后端        │
│                 │◄──►│                  │◄──►│                 │
│ WebSocket Client│    │ FastAPI+WebSocket│    │ Lightning MLX   │
│                 │    │ Audio Buffer Mgr │    │ (Mac)           │
│                 │    │ VAD + Sliding    │    │ Faster-Whisper  │
│                 │    │ Window           │    │ (Win/Linux)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 核心组件实现

#### 1. 音频缓冲管理器
```python
class SlidingWindowBuffer:
    def __init__(self, step_ms=3000, length_ms=10000, keep_ms=200, sample_rate=16000):
        self.step_ms = step_ms
        self.length_ms = length_ms  
        self.keep_ms = keep_ms
        self.sample_rate = sample_rate
        
        self.step_samples = int(step_ms * sample_rate / 1000)
        self.length_samples = int(length_ms * sample_rate / 1000)
        self.keep_samples = int(keep_ms * sample_rate / 1000)
        
        self.audio_buffer = np.array([], dtype=np.float32)
        self.previous_audio = np.array([], dtype=np.float32)
        
    def add_audio(self, new_audio: np.ndarray) -> Optional[np.ndarray]:
        """添加新音频，返回准备处理的窗口"""
        self.audio_buffer = np.concatenate([self.audio_buffer, new_audio])
        
        if len(self.audio_buffer) >= self.step_samples:
            # 构建处理窗口：保留音频 + 新音频
            n_take = min(len(self.previous_audio), 
                        max(0, self.keep_samples + self.length_samples - len(self.audio_buffer)))
            
            if n_take > 0:
                window = np.concatenate([
                    self.previous_audio[-n_take:],
                    self.audio_buffer
                ])
            else:
                window = self.audio_buffer.copy()
            
            # 更新状态
            self.previous_audio = window.copy()
            self.audio_buffer = np.array([], dtype=np.float32)
            
            return window[:self.length_samples]  # 限制最大长度
        
        return None
```

#### 2. VAD 实现
```python
class SimpleVAD:
    def __init__(self, threshold=0.6, freq_threshold=100.0):
        self.vad_threshold = threshold
        self.freq_threshold = freq_threshold
    
    def is_speech(self, audio: np.ndarray, sample_rate: int, last_ms: int = 1000) -> bool:
        """简单的语音活动检测"""
        n_samples = len(audio)
        n_samples_last = int(sample_rate * last_ms / 1000)
        
        if n_samples_last >= n_samples:
            return False
        
        # 高通滤波
        if self.freq_threshold > 0:
            audio = self._high_pass_filter(audio, self.freq_threshold, sample_rate)
        
        # 计算能量
        energy_all = np.mean(np.abs(audio))
        energy_last = np.mean(np.abs(audio[-n_samples_last:]))
        
        return energy_last > self.vad_threshold * energy_all
    
    def _high_pass_filter(self, data: np.ndarray, cutoff: float, sample_rate: int) -> np.ndarray:
        """简单的高通滤波器"""
        from scipy import signal
        nyquist = sample_rate / 2
        normal_cutoff = cutoff / nyquist
        b, a = signal.butter(1, normal_cutoff, btype='high', analog=False)
        return signal.filtfilt(b, a, data)
```

#### 3. 跨平台推理后端
```python
class CrossPlatformWhisper:
    def __init__(self):
        self.backend = self._detect_optimal_backend()
        self.model = self._load_model()
    
    def _detect_optimal_backend(self) -> str:
        """检测最优后端"""
        import platform
        
        if platform.system() == "Darwin" and platform.machine() == "arm64":
            try:
                import lightning_whisper_mlx
                return "lightning-mlx"
            except ImportError:
                pass
        
        try:
            import faster_whisper
            return "faster-whisper"
        except ImportError:
            pass
        
        return "openai-whisper"  # 备选方案
    
    def _load_model(self):
        """加载对应的模型"""
        if self.backend == "lightning-mlx":
            from lightning_whisper_mlx import LightningWhisperMLX
            return LightningWhisperMLX(
                model="distil-small.en",
                batch_size=12,
                quant="4bit"
            )
        elif self.backend == "faster-whisper":
            from faster_whisper import WhisperModel
            return WhisperModel("small", device="auto")
        else:
            import whisper
            return whisper.load_model("small")
    
    def transcribe(self, audio: np.ndarray, language: str = "auto") -> dict:
        """统一的转录接口"""
        if self.backend == "lightning-mlx":
            # 需要保存为临时文件
            import tempfile, soundfile as sf
            with tempfile.NamedTemporaryFile(suffix=".wav") as f:
                sf.write(f.name, audio, 16000)
                result = self.model.transcribe(f.name, language=language if language != "auto" else None)
                return {
                    "text": result.get("text", ""),
                    "segments": result.get("segments", []),
                    "language": result.get("language", "unknown")
                }
        elif self.backend == "faster-whisper":
            segments, info = self.model.transcribe(audio, language=language if language != "auto" else None)
            text = " ".join([segment.text for segment in segments])
            return {
                "text": text,
                "segments": list(segments),
                "language": info.language
            }
        else:
            result = self.model.transcribe(audio, language=language if language != "auto" else None)
            return result
```

## 🎯 实施优势

### 1. 性能优化
- **Mac**: Lightning Whisper MLX (10x faster)
- **Windows/Linux**: Faster-Whisper (GPU 加速)
- **通用**: OpenAI Whisper (兼容性备选)

### 2. 实时性保证
- 滑动窗口避免重复计算
- VAD 减少无效处理
- 上下文保持提高准确性

### 3. 跨平台兼容
- 统一的 Python 接口
- 自动后端检测和切换
- 相同的 WebSocket 协议

## 📋 下一步实施计划

1. **实现核心组件** (1-2 天)
   - SlidingWindowBuffer
   - SimpleVAD  
   - CrossPlatformWhisper

2. **构建 WebSocket 服务** (1 天)
   - FastAPI 服务器
   - 音频流处理
   - 结果格式化

3. **前端适配** (0.5 天)
   - 保持现有接口
   - 添加后端切换

4. **测试和优化** (1 天)
   - 性能基准测试
   - 延迟优化
   - 稳定性验证

这个方案完美结合了 whisper.cpp 的成熟架构和现代 Python 生态的优势！
