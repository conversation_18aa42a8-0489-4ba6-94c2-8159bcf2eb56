<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跨平台 Whisper 流式服务测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 1000px; 
            margin: 0 auto; 
            padding: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 8px; 
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        
        button { 
            padding: 12px 24px; 
            margin: 8px; 
            font-size: 16px; 
            border: none; 
            border-radius: 6px; 
            cursor: pointer; 
            transition: all 0.3s;
        }
        button:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        button:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        
        .record-btn { background: linear-gradient(45deg, #dc3545, #c82333); color: white; }
        .stop-btn { background: linear-gradient(45deg, #28a745, #218838); color: white; }
        .connect-btn { background: linear-gradient(45deg, #007bff, #0056b3); color: white; }
        .test-btn { background: linear-gradient(45deg, #17a2b8, #138496); color: white; }
        
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        
        #output { 
            border: 1px solid #ddd; 
            padding: 15px; 
            height: 300px; 
            overflow-y: auto; 
            white-space: pre-wrap; 
            font-family: 'Courier New', monospace; 
            background-color: #f8f9fa;
            border-radius: 6px;
        }
        
        #transcription { 
            border: 1px solid #ddd; 
            padding: 15px; 
            min-height: 120px; 
            background-color: #f8f9fa;
            border-radius: 6px;
            margin-top: 10px;
        }
        
        .backend-info {
            background: linear-gradient(45deg, #6f42c1, #5a32a3);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 14px;
            margin-top: 5px;
        }
        
        h1 { color: #343a40; text-align: center; margin-bottom: 30px; }
        h3 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 增强版 Whisper 流式服务测试</h1>

        <div class="controls">
            <button id="healthBtn" class="test-btn">健康检查</button>
            <button id="connectBtn" class="connect-btn">连接 WebSocket</button>
            <button id="recordBtn" class="record-btn" disabled>开始录音</button>
            <button id="stopBtn" class="stop-btn" disabled>停止录音</button>
            <button id="clearBtn">清空日志</button>
            <button id="switchBtn" class="test-btn">切换服务</button>
        </div>

        <div id="status" class="status info">准备连接到增强版服务 ws://localhost:8892/ws</div>
        
        <div id="backendInfo" class="backend-info" style="display: none;">
            <h4>🔧 后端信息</h4>
            <div id="backendDetails"></div>
        </div>
        
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value" id="audioPackets">0</div>
                <div class="metric-label">音频包数量</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="transcriptionCount">0</div>
                <div class="metric-label">转录次数</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="avgLatency">0ms</div>
                <div class="metric-label">平均延迟</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="connectionTime">0s</div>
                <div class="metric-label">连接时长</div>
            </div>
        </div>
        
        <h3>📋 实时日志</h3>
        <div id="output"></div>
        
        <h3>🔊 转录结果</h3>
        <div id="transcription"></div>
    </div>

    <script>
        let websocket = null;
        let mediaRecorder = null;
        let audioStream = null;
        let isRecording = false;
        let connectionStartTime = null;
        let audioPacketCount = 0;
        let transcriptionCount = 0;
        let latencySum = 0;
        let latencyCount = 0;

        // 服务配置
        let currentService = 'enhanced'; // 'enhanced' 或 'basic'
        const serviceConfig = {
            enhanced: {
                port: 8892,
                name: '增强版服务',
                features: ['Silero VAD', '智能分割', '部分更新', '说话人分离']
            },
            basic: {
                port: 8891,
                name: '基础版服务',
                features: ['基础 VAD', '简单分割']
            }
        };

        const healthBtn = document.getElementById('healthBtn');
        const connectBtn = document.getElementById('connectBtn');
        const recordBtn = document.getElementById('recordBtn');
        const stopBtn = document.getElementById('stopBtn');
        const clearBtn = document.getElementById('clearBtn');
        const switchBtn = document.getElementById('switchBtn');
        const statusDiv = document.getElementById('status');
        const outputDiv = document.getElementById('output');
        const transcriptionDiv = document.getElementById('transcription');
        const backendInfoDiv = document.getElementById('backendInfo');
        const backendDetailsDiv = document.getElementById('backendDetails');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            outputDiv.textContent += logMessage;
            outputDiv.scrollTop = outputDiv.scrollHeight;
            
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function updateMetrics() {
            document.getElementById('audioPackets').textContent = audioPacketCount;
            document.getElementById('transcriptionCount').textContent = transcriptionCount;
            
            const avgLatency = latencyCount > 0 ? Math.round(latencySum / latencyCount) : 0;
            document.getElementById('avgLatency').textContent = `${avgLatency}ms`;
            
            if (connectionStartTime) {
                const connectionTime = Math.round((Date.now() - connectionStartTime) / 1000);
                document.getElementById('connectionTime').textContent = `${connectionTime}s`;
            }
        }

        function getCurrentServiceUrl(endpoint = '') {
            const config = serviceConfig[currentService];
            return `http://localhost:${config.port}${endpoint}`;
        }

        function getCurrentWebSocketUrl() {
            const config = serviceConfig[currentService];
            return `ws://localhost:${config.port}/ws`;
        }

        function switchService() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                log("请先断开当前连接再切换服务", "warning");
                return;
            }

            currentService = currentService === 'enhanced' ? 'basic' : 'enhanced';
            const config = serviceConfig[currentService];

            log(`切换到 ${config.name} (端口 ${config.port})`, "info");
            log(`功能: ${config.features.join(', ')}`, "info");

            statusDiv.textContent = `准备连接到 ${config.name} ${getCurrentWebSocketUrl()}`;

            // 更新后端信息显示
            backendDetailsDiv.innerHTML = `
                <p><strong>当前服务:</strong> ${config.name}</p>
                <p><strong>端口:</strong> ${config.port}</p>
                <p><strong>功能:</strong> ${config.features.join(', ')}</p>
            `;
            backendInfoDiv.style.display = 'block';
        }

        async function healthCheck() {
            try {
                const config = serviceConfig[currentService];
                log(`🔍 执行 ${config.name} 健康检查...`, "info");
                const response = await fetch(getCurrentServiceUrl('/health'));
                const data = await response.json();
                
                log(`✅ 健康检查成功: ${data.status}`, "success");
                log(`后端: ${data.backend}`, "info");
                if (data.platform) log(`平台: ${data.platform}`, "info");

                let featuresHtml = '';
                if (data.features) {
                    featuresHtml = `<p><strong>功能:</strong></p><ul>`;
                    for (const [feature, enabled] of Object.entries(data.features)) {
                        if (feature === 'speaker_backend') {
                            if (enabled) {
                                featuresHtml += `<li>✅ 说话人分离后端: ${enabled}</li>`;
                            }
                        } else {
                            const status = enabled ? '✅' : '❌';
                            const featureNames = {
                                'silero_vad': 'Silero VAD',
                                'intelligent_segmentation': '智能句子分割',
                                'partial_updates': '部分更新管理',
                                'speaker_diarization': '说话人分离'
                            };
                            const displayName = featureNames[feature] || feature;
                            featuresHtml += `<li>${status} ${displayName}</li>`;
                        }
                    }
                    featuresHtml += `</ul>`;
                }

                backendDetailsDiv.innerHTML = `
                    <p><strong>状态:</strong> ${data.status}</p>
                    <p><strong>后端:</strong> ${data.backend}</p>
                    ${data.platform ? `<p><strong>平台:</strong> ${data.platform}</p>` : ''}
                    <p><strong>服务:</strong> ${data.service}</p>
                    ${featuresHtml}
                `;
                backendInfoDiv.style.display = 'block';
                
            } catch (error) {
                log(`❌ 健康检查失败: ${error.message}`, "error");
            }
        }

        function connectWebSocket() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                log("WebSocket 已经连接", "info");
                return;
            }

            log("正在连接 WebSocket...", "info");
            websocket = new WebSocket(getCurrentWebSocketUrl());
            connectionStartTime = Date.now();

            websocket.onopen = function() {
                log("✅ WebSocket 连接成功！", "success");
                connectBtn.disabled = true;
                recordBtn.disabled = false;
                updateUI();
            };

            websocket.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    const messageTime = Date.now();
                    
                    log(`📥 收到消息: ${data.type}`, "info");

                    if (data.type === 'CONNECTION_SUCCESS') {
                        log(`🎉 服务器连接确认: ${data.message}`, "success");
                        log(`后端: ${data.backend}`, "info");
                        if (data.config) {
                            log(`配置: step=${data.config.step_ms}ms, length=${data.config.length_ms}ms, keep=${data.config.keep_ms}ms`, "info");
                        }
                        
                        backendDetailsDiv.innerHTML += `
                            <p><strong>WebSocket 后端:</strong> ${data.backend}</p>
                            <p><strong>配置:</strong> ${JSON.stringify(data.config, null, 2)}</p>
                        `;
                        
                    } else if (data.type === 'TRANSCRIPTION_RESULT') {
                        transcriptionCount++;

                        // 计算延迟
                        if (data.timestamp) {
                            const latency = messageTime - (data.timestamp * 1000);
                            latencySum += latency;
                            latencyCount++;
                        }

                        if (data.text && data.text.trim()) {
                            const backendLabel = data.backend ? `[${data.backend}]` : '';
                            const languageLabel = data.language ? `(${data.language})` : '';

                            transcriptionDiv.innerHTML += `
                                <div style="margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; background-color: #f8f9fa;">
                                    <strong>${backendLabel} ${languageLabel}:</strong> ${data.text}
                                    <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">
                                        时间: ${new Date().toLocaleTimeString()}
                                        ${data.segments ? ` | 段落: ${data.segments.length}` : ''}
                                    </div>
                                </div>
                            `;

                            log(`🗣️ 转录 [${data.backend}]: ${data.text}`, "success");
                            if (data.segments && data.segments.length > 0) {
                                log(`📊 段落数: ${data.segments.length}`, "info");
                            }
                        }

                    } else if (data.type === 'ENHANCED_TRANSCRIPTION_RESULT') {
                        // 处理增强版转录结果
                        transcriptionCount++;

                        if (data.timestamp) {
                            const latency = messageTime - (data.timestamp * 1000);
                            latencySum += latency;
                            latencyCount++;
                        }

                        // 显示确认的句子
                        if (data.confirmed_sentences && data.confirmed_sentences.length > 0) {
                            data.confirmed_sentences.forEach(sentence => {
                                const speakerLabel = sentence.speaker_id !== null && sentence.speaker_id !== undefined
                                    ? ` | 说话人: Speaker ${sentence.speaker_id}`
                                    : '';
                                const speakerColor = sentence.speaker_id !== null && sentence.speaker_id !== undefined
                                    ? ['#007bff', '#28a745', '#dc3545', '#ffc107', '#17a2b8'][sentence.speaker_id % 5]
                                    : '#28a745';

                                transcriptionDiv.innerHTML += `
                                    <div style="margin: 10px 0; padding: 10px; border-left: 4px solid ${speakerColor}; background-color: #f8f9fa;">
                                        <strong>[${data.backend}] 确认句子:</strong> ${sentence.text}
                                        <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">
                                            置信度: ${(sentence.confidence * 100).toFixed(1)}% |
                                            时间: ${sentence.start_time.toFixed(1)}s - ${sentence.end_time.toFixed(1)}s
                                            ${speakerLabel}
                                        </div>
                                    </div>
                                `;
                            });
                            log(`✅ 确认句子: ${data.confirmed_sentences.length} 个`, "success");
                        }

                        // 显示说话人分离信息
                        if (data.speaker_info && data.speaker_info.speakers && data.speaker_info.speakers.length > 0) {
                            log(`👥 说话人: ${data.speaker_info.speakers.join(', ')} (置信度: ${(data.speaker_info.confidence * 100).toFixed(1)}%)`, "info");
                            if (data.speaker_info.speaker_changes) {
                                log(`🔄 检测到说话人变化`, "warning");
                            }
                        }

                        // 显示待确认文本
                        if (data.pending_text && data.pending_text.text) {
                            const pendingDiv = document.getElementById('pending-text') || (() => {
                                const div = document.createElement('div');
                                div.id = 'pending-text';
                                div.style.cssText = 'margin: 10px 0; padding: 10px; border-left: 4px solid #ffc107; background-color: #fff3cd; font-style: italic;';
                                transcriptionDiv.appendChild(div);
                                return div;
                            })();

                            pendingDiv.innerHTML = `
                                <strong>[${data.backend}] 待确认:</strong> ${data.pending_text.text}
                                <div style="font-size: 12px; color: #856404; margin-top: 5px;">
                                    置信度: ${(data.pending_text.confidence * 100).toFixed(1)}%
                                </div>
                            `;
                        }

                        // 显示变化分析
                        if (data.change_analysis && data.change_analysis.type !== 'initial') {
                            log(`🔄 文本变化: ${data.change_analysis.type} (相似度: ${(data.change_analysis.similarity * 100).toFixed(1)}%)`, "info");
                        }
                        
                    } else if (data.type === 'NO_SPEECH_DETECTED') {
                        log(`🔇 未检测到语音`, "warning");
                        
                    } else if (data.type === 'TRANSCRIPTION_COMPLETE') {
                        log(`🏁 转录完成: ${data.message}`, "success");
                        
                    } else if (data.type === 'ERROR') {
                        log(`❌ 错误: ${data.message}`, "error");
                        
                    } else {
                        log(`📋 其他消息: ${JSON.stringify(data)}`, "info");
                    }
                    
                    updateMetrics();
                    
                } catch (e) {
                    log(`❌ 解析消息失败: ${e.message}`, "error");
                    log(`原始数据: ${event.data}`, "error");
                }
            };

            websocket.onerror = function(error) {
                log(`❌ WebSocket 错误: ${error}`, "error");
            };

            websocket.onclose = function() {
                log("🔌 WebSocket 连接已关闭", "info");
                connectBtn.disabled = false;
                recordBtn.disabled = true;
                stopBtn.disabled = true;
                connectionStartTime = null;
                updateUI();
            };
        }

        async function startRecording() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                log("❌ WebSocket 未连接", "error");
                return;
            }

            try {
                log("🎤 请求麦克风权限...", "info");
                audioStream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    } 
                });

                log("✅ 麦克风权限获取成功", "success");

                mediaRecorder = new MediaRecorder(audioStream, { 
                    mimeType: 'audio/webm;codecs=opus'
                });

                mediaRecorder.ondataavailable = function(event) {
                    if (event.data.size > 0 && websocket && websocket.readyState === WebSocket.OPEN) {
                        websocket.send(event.data);
                        audioPacketCount++;
                        log(`📤 发送音频数据: ${event.data.size} 字节 (包 #${audioPacketCount})`, "info");
                        updateMetrics();
                    }
                };

                mediaRecorder.onstart = function() {
                    log("🔴 录音开始", "success");
                    isRecording = true;
                    updateUI();
                };

                mediaRecorder.onstop = function() {
                    log("⏹️ 录音停止", "info");
                    isRecording = false;
                    updateUI();
                };

                // 开始录音，每 500ms 发送一次数据
                mediaRecorder.start(500);

            } catch (error) {
                log(`❌ 录音启动失败: ${error.message}`, "error");
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                
                if (websocket && websocket.readyState === WebSocket.OPEN) {
                    const stopMessage = JSON.stringify({ type: 'STOP_RECORDING' });
                    websocket.send(stopMessage);
                    log("📤 发送停止信号", "info");
                }
            }

            if (audioStream) {
                audioStream.getTracks().forEach(track => track.stop());
                audioStream = null;
            }
        }

        function updateUI() {
            recordBtn.disabled = !websocket || websocket.readyState !== WebSocket.OPEN || isRecording;
            stopBtn.disabled = !isRecording;
        }

        function clearOutput() {
            outputDiv.textContent = '';
            transcriptionDiv.innerHTML = '';
            audioPacketCount = 0;
            transcriptionCount = 0;
            latencySum = 0;
            latencyCount = 0;
            updateMetrics();
        }

        // 事件监听器
        healthBtn.addEventListener('click', healthCheck);
        connectBtn.addEventListener('click', connectWebSocket);
        recordBtn.addEventListener('click', startRecording);
        stopBtn.addEventListener('click', stopRecording);
        clearBtn.addEventListener('click', clearOutput);
        switchBtn.addEventListener('click', switchService);

        // 定时更新连接时长
        setInterval(updateMetrics, 1000);

        // 页面加载完成后的初始化
        log("🚀 增强版 Whisper 流式服务测试页面已加载", "info");
        log(`当前服务: ${serviceConfig[currentService].name}`, "info");
        log("请先点击'健康检查'验证服务状态", "info");
        updateMetrics();
    </script>
</body>
</html>
