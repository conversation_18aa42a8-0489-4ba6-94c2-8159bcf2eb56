# WhisperSync 项目启动规则

## 🚀 启动命令
- **开发环境**: `npm run electron:dev`
  - 同时启动: Vite开发服务器 + TypeScript编译 + Electron应用 + Whisper服务器
  - 端口: http://localhost:5173
  - 自动重载: 支持热更新

- **仅Web开发**: `npm run dev` 
  - 只启动Vite开发服务器，用于前端开发调试

- **仅Whisper服务器**: `npm run start:whisper-server`
  - 只启动语音识别后端服务

## 🛠️ 其他常用命令
- **构建**: `npm run build`
- **清理**: `npm run clean`
- **重置**: `npm run reset`
- **下载模型**: `npm run download:models`

## 📁 项目结构
- `src/`: React前端源码
- `electron/`: Electron主进程代码
- `scripts/`: 启动脚本和工具
- `whisper.cpp/`: Whisper C++编译文件
- `models/`: AI模型文件

## 🔧 开发注意事项
- 项目类型: Electron + React + Vite + TypeScript
- 语音识别: whisper.cpp + VAD模型
- 通信方式: WebSocket (ws://localhost:3001)
- 热更新: 支持前端和Electron主进程

## 🐛 调试
- 前端调试: Electron DevTools (Ctrl+Shift+I)
- 后端日志: 终端输出 (whisper服务器日志)
- 网络调试: WebSocket连接状态

## 📝 最近修改
- 修复了转录显示的重复问题
- 实现了正确的增量显示逻辑
- 优化了打字效果的状态管理 