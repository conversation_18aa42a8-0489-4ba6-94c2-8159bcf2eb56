#!/usr/bin/env python3
"""
基于 whisper.cpp 架构的跨平台流式语音识别服务
支持 Lightning Whisper MLX (Mac) 和 Faster-Whisper (Win/Linux)
"""

import asyncio
import json
import logging
import numpy as np
import platform
import tempfile
import os
from typing import Optional, Dict, Any, AsyncGenerator
from dataclasses import dataclass
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class StreamingConfig:
    """流式配置参数（模仿 whisper.cpp）"""
    step_ms: int = 3000      # 步进间隔（毫秒）
    length_ms: int = 10000   # 处理窗口长度（毫秒）
    keep_ms: int = 200       # 重叠保留时间（毫秒）
    sample_rate: int = 16000 # 采样率
    vad_threshold: float = 0.6    # VAD 阈值
    freq_threshold: float = 100.0 # 高通滤波阈值
    use_vad: bool = False    # 是否使用 VAD 模式
    language: str = "auto"   # 语言设置
    no_context: bool = False # 是否禁用上下文

class SlidingWindowBuffer:
    """滑动窗口音频缓冲器（基于 whisper.cpp 设计）"""
    
    def __init__(self, config: StreamingConfig):
        self.config = config
        
        # 计算样本数
        self.step_samples = int(config.step_ms * config.sample_rate / 1000)
        self.length_samples = int(config.length_ms * config.sample_rate / 1000)
        self.keep_samples = int(config.keep_ms * config.sample_rate / 1000)
        
        # 音频缓冲区
        self.audio_buffer = np.array([], dtype=np.float32)
        self.previous_audio = np.array([], dtype=np.float32)
        self.accumulated_samples = 0
        
        logger.info(f"滑动窗口初始化: step={config.step_ms}ms, length={config.length_ms}ms, keep={config.keep_ms}ms")
    
    def add_audio(self, new_audio: np.ndarray) -> Optional[np.ndarray]:
        """添加新音频数据，返回准备处理的窗口"""
        self.audio_buffer = np.concatenate([self.audio_buffer, new_audio])
        self.accumulated_samples += len(new_audio)
        
        # 检查是否达到步进阈值
        if len(self.audio_buffer) >= self.step_samples:
            return self._create_processing_window()
        
        return None
    
    def _create_processing_window(self) -> np.ndarray:
        """创建处理窗口（模仿 whisper.cpp 逻辑）"""
        n_samples_new = len(self.audio_buffer)
        
        # 计算需要从上一次保留的样本数
        n_samples_take = min(
            len(self.previous_audio),
            max(0, self.keep_samples + self.length_samples - n_samples_new)
        )
        
        # 构建处理窗口：旧音频尾部 + 新音频
        if n_samples_take > 0:
            window = np.concatenate([
                self.previous_audio[-n_samples_take:],
                self.audio_buffer
            ])
        else:
            window = self.audio_buffer.copy()
        
        # 限制窗口最大长度
        if len(window) > self.length_samples:
            window = window[:self.length_samples]
        
        # 更新状态
        self.previous_audio = window.copy()
        self.audio_buffer = np.array([], dtype=np.float32)
        
        logger.debug(f"创建处理窗口: {len(window)} 样本 ({len(window)/self.config.sample_rate:.2f}秒)")
        return window

class SimpleVAD:
    """简单的语音活动检测（基于 whisper.cpp VAD）"""
    
    def __init__(self, vad_threshold: float = 0.6, freq_threshold: float = 100.0):
        self.vad_threshold = vad_threshold
        self.freq_threshold = freq_threshold
    
    def is_speech(self, audio: np.ndarray, sample_rate: int, last_ms: int = 1000) -> bool:
        """检测是否包含语音"""
        n_samples = len(audio)
        n_samples_last = int(sample_rate * last_ms / 1000)
        
        if n_samples_last >= n_samples:
            return False
        
        # 应用高通滤波
        if self.freq_threshold > 0:
            audio = self._high_pass_filter(audio, self.freq_threshold, sample_rate)
        
        # 计算能量
        energy_all = np.mean(np.abs(audio))
        energy_last = np.mean(np.abs(audio[-n_samples_last:]))
        
        # VAD 判断
        is_speech = energy_last > self.vad_threshold * energy_all
        
        logger.debug(f"VAD: energy_all={energy_all:.4f}, energy_last={energy_last:.4f}, is_speech={is_speech}")
        return is_speech
    
    def _high_pass_filter(self, data: np.ndarray, cutoff: float, sample_rate: int) -> np.ndarray:
        """简单的高通滤波器"""
        try:
            from scipy import signal
            nyquist = sample_rate / 2
            normal_cutoff = cutoff / nyquist
            b, a = signal.butter(1, normal_cutoff, btype='high', analog=False)
            return signal.filtfilt(b, a, data)
        except ImportError:
            logger.warning("scipy 未安装，跳过高通滤波")
            return data

class CrossPlatformWhisper:
    """跨平台 Whisper 推理后端"""
    
    def __init__(self, model_size: str = "small"):
        self.backend_type = self._detect_optimal_backend()
        self.model_size = model_size
        self.model = self._load_model()
        self.context_tokens = []  # 上下文 tokens
        
        logger.info(f"使用后端: {self.backend_type}, 模型: {model_size}")
    
    def _detect_optimal_backend(self) -> str:
        """检测最优后端"""
        system = platform.system()
        machine = platform.machine()
        
        # Apple Silicon 优先使用 Lightning MLX
        if system == "Darwin" and machine == "arm64":
            try:
                import lightning_whisper_mlx
                return "lightning-mlx"
            except ImportError:
                logger.warning("lightning-whisper-mlx 未安装，降级到 faster-whisper")
        
        # 其他平台使用 faster-whisper
        try:
            import faster_whisper
            return "faster-whisper"
        except ImportError:
            logger.warning("faster-whisper 未安装，使用 openai-whisper")
            return "openai-whisper"
    
    def _load_model(self):
        """加载对应的模型"""
        if self.backend_type == "lightning-mlx":
            from lightning_whisper_mlx import LightningWhisperMLX
            # 根据模型大小选择合适的配置
            model_name = "distil-small.en" if self.model_size == "small" else f"distil-{self.model_size}.en"
            return LightningWhisperMLX(
                model=model_name,
                batch_size=12,
                quant=None  # 禁用量化避免兼容性问题
            )
        elif self.backend_type == "faster-whisper":
            from faster_whisper import WhisperModel
            return WhisperModel(
                self.model_size,
                device="auto",
                compute_type="float16"
            )
        else:
            import whisper
            return whisper.load_model(self.model_size)
    
    async def transcribe_async(self, audio: np.ndarray, language: str = "auto") -> Dict[str, Any]:
        """异步转录音频"""
        # 在线程池中执行转录以避免阻塞
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self._transcribe_sync, audio, language)
    
    def _transcribe_sync(self, audio: np.ndarray, language: str = "auto") -> Dict[str, Any]:
        """同步转录音频"""
        try:
            if self.backend_type == "lightning-mlx":
                return self._transcribe_lightning_mlx(audio, language)
            elif self.backend_type == "faster-whisper":
                return self._transcribe_faster_whisper(audio, language)
            else:
                return self._transcribe_openai_whisper(audio, language)
        except Exception as e:
            logger.error(f"转录失败: {e}")
            return {"text": "", "segments": [], "language": "unknown", "error": str(e)}
    
    def _transcribe_lightning_mlx(self, audio: np.ndarray, language: str) -> Dict[str, Any]:
        """Lightning MLX 转录"""
        import soundfile as sf
        
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as f:
            sf.write(f.name, audio, 16000)
            
            result = self.model.transcribe(
                f.name,
                language=language if language != "auto" else None
            )
            
            os.unlink(f.name)
            
            return {
                "text": result.get("text", "").strip(),
                "segments": result.get("segments", []),
                "language": result.get("language", "unknown")
            }
    
    def _transcribe_faster_whisper(self, audio: np.ndarray, language: str) -> Dict[str, Any]:
        """Faster-Whisper 转录"""
        segments, info = self.model.transcribe(
            audio,
            language=language if language != "auto" else None,
            initial_prompt=" ".join(self.context_tokens[-50:]) if self.context_tokens else None
        )
        
        segments_list = list(segments)
        text = " ".join([segment.text for segment in segments_list])
        
        # 更新上下文
        if segments_list and not self.context_tokens:
            self.context_tokens.extend([segment.text for segment in segments_list])
        
        return {
            "text": text.strip(),
            "segments": [{"text": s.text, "start": s.start, "end": s.end} for s in segments_list],
            "language": info.language
        }
    
    def _transcribe_openai_whisper(self, audio: np.ndarray, language: str) -> Dict[str, Any]:
        """OpenAI Whisper 转录"""
        result = self.model.transcribe(
            audio,
            language=language if language != "auto" else None,
            initial_prompt=" ".join(self.context_tokens[-50:]) if self.context_tokens else None
        )
        
        # 更新上下文
        if result.get("text") and not self.context_tokens:
            self.context_tokens.append(result["text"])
        
        return {
            "text": result.get("text", "").strip(),
            "segments": result.get("segments", []),
            "language": result.get("language", "unknown")
        }

class WhisperStreamingService:
    """流式语音识别服务"""
    
    def __init__(self, config: StreamingConfig = None):
        self.config = config or StreamingConfig()
        self.whisper = CrossPlatformWhisper()
        self.vad = SimpleVAD(self.config.vad_threshold, self.config.freq_threshold)
        
    async def process_audio_stream(self, websocket: WebSocket):
        """处理音频流"""
        buffer = SlidingWindowBuffer(self.config)
        
        try:
            while True:
                # 接收音频数据
                message = await websocket.receive()
                
                if message.get("type") == "websocket.receive" and "bytes" in message:
                    audio_data = message["bytes"]
                    
                    if len(audio_data) == 0:
                        # 空数据表示结束
                        await websocket.send_json({
                            "type": "TRANSCRIPTION_COMPLETE",
                            "message": "音频流结束"
                        })
                        break
                    
                    # 转换音频数据
                    audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
                    
                    # 添加到缓冲区
                    window = buffer.add_audio(audio_array)
                    
                    if window is not None:
                        # 检查是否需要 VAD
                        if self.config.use_vad:
                            if not self.vad.is_speech(window, self.config.sample_rate):
                                await websocket.send_json({
                                    "type": "NO_SPEECH_DETECTED",
                                    "message": "未检测到语音"
                                })
                                continue
                        
                        # 执行转录
                        result = await self.whisper.transcribe_async(window, self.config.language)
                        
                        # 发送结果
                        await websocket.send_json({
                            "type": "TRANSCRIPTION_RESULT",
                            "text": result["text"],
                            "segments": result["segments"],
                            "language": result["language"],
                            "backend": self.whisper.backend_type,
                            "timestamp": asyncio.get_event_loop().time()
                        })
                
                elif message.get("type") == "websocket.receive" and "text" in message:
                    # 处理控制消息
                    try:
                        control_msg = json.loads(message["text"])
                        if control_msg.get("type") == "STOP_RECORDING":
                            break
                    except json.JSONDecodeError:
                        pass
                        
        except WebSocketDisconnect:
            logger.info("WebSocket 连接断开")
        except Exception as e:
            logger.error(f"音频流处理错误: {e}")
            await websocket.send_json({
                "type": "ERROR",
                "message": f"处理错误: {str(e)}"
            })

# FastAPI 应用
app = FastAPI(title="跨平台 Whisper 流式服务")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局服务实例
streaming_service: Optional[WhisperStreamingService] = None

@app.on_event("startup")
async def startup_event():
    """启动时初始化服务"""
    global streaming_service
    try:
        config = StreamingConfig(
            step_ms=3000,      # 3秒步进
            length_ms=10000,   # 10秒窗口
            keep_ms=200,       # 200ms重叠
            use_vad=False,     # 暂时禁用VAD
            language="auto"
        )
        streaming_service = WhisperStreamingService(config)
        logger.info("🚀 跨平台 Whisper 流式服务启动成功")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        raise

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "跨平台 Whisper 流式服务",
        "backend": streaming_service.whisper.backend_type if streaming_service else "未初始化",
        "platform": f"{platform.system()} {platform.machine()}"
    }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket 端点"""
    await websocket.accept()
    logger.info("🔌 WebSocket 连接已建立")
    
    await websocket.send_json({
        "type": "CONNECTION_SUCCESS",
        "message": "跨平台 Whisper 流式服务已连接",
        "backend": streaming_service.whisper.backend_type,
        "config": {
            "step_ms": streaming_service.config.step_ms,
            "length_ms": streaming_service.config.length_ms,
            "keep_ms": streaming_service.config.keep_ms
        }
    })
    
    await streaming_service.process_audio_stream(websocket)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8891)
