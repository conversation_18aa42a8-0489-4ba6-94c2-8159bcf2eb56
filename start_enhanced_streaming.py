#!/usr/bin/env python3
"""
增强版跨平台 Whisper 流式服务启动脚本
"""

import subprocess
import sys
import os

def main():
    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    service_path = os.path.join(script_dir, "enhanced_whisper_streaming.py")
    
    if not os.path.exists(service_path):
        print(f"❌ 增强版服务文件不存在: {service_path}")
        return 1
    
    print("🚀 启动增强版跨平台 Whisper 流式服务...")
    print("服务地址: http://localhost:8892")
    print("WebSocket: ws://localhost:8892/ws")
    print("健康检查: http://localhost:8892/health")
    print("")
    print("🎯 增强功能:")
    print("  ✅ Silero VAD 语音活动检测")
    print("  ✅ 智能句子分割")
    print("  ✅ 部分更新管理")
    print("  ⏳ 说话人分离 (待实现)")
    print("")
    print("按 Ctrl+C 停止服务")
    print("-" * 60)
    
    try:
        subprocess.run([sys.executable, service_path], check=True)
    except KeyboardInterrupt:
        print("\n👋 增强版服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 增强版服务启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
