@import "tailwindcss";

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#root {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%);
  color: #ffffff;
}

/* 主应用容器 */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%);
  overflow: hidden;
}

/* 状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.connection-status {
  font-size: 1rem;
  color: #e0e0e0;
  font-weight: 500;
}

.status-controls {
  display: flex;
  gap: 0.75rem;
}

.status-controls button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.test-button {
  background: rgba(138, 43, 226, 0.8);
  color: white;
}

.test-button:hover:not(:disabled) {
  background: rgba(138, 43, 226, 1);
  transform: translateY(-1px);
}

.clear-button {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.clear-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* 主内容区域 */
.main-content {
  display: flex;
  flex: 1;
  gap: 2rem;
  padding: 2rem;
  overflow: hidden;
}

/* 左侧控制面板 */
.left-panel {
  flex: 0 0 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-text {
  font-size: 1.2rem;
  color: #e0e0e0;
  margin-bottom: 2rem;
  text-align: center;
  font-weight: 500;
}

.record-button {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #8a2be2, #9d4edd);
  color: white;
  font-size: 2.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(138, 43, 226, 0.3);
  margin-bottom: 2rem;
  position: relative;
}

.record-button:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 12px 40px rgba(138, 43, 226, 0.4);
}

.record-button.recording {
  background: linear-gradient(135deg, #dc3545, #e91e63);
  animation: pulse-glow 1.5s infinite;
}

@keyframes pulse-glow {
  0% { 
    transform: scale(1); 
    box-shadow: 0 8px 32px rgba(220, 53, 69, 0.3);
  }
  50% { 
    transform: scale(1.05); 
    box-shadow: 0 12px 40px rgba(220, 53, 69, 0.6);
  }
  100% { 
    transform: scale(1); 
    box-shadow: 0 8px 32px rgba(220, 53, 69, 0.3);
  }
}

/* 字幕测试页面样式 */
.subtitle-test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%);
  color: #ffffff;
  display: flex;
  flex-direction: column;
}

.test-header {
  padding: 2rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-top {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 1rem;
}

.test-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #8a2be2, #9d4edd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.test-header p {
  font-size: 1.2rem;
  color: #b0b0b8;
  margin-bottom: 2rem;
}

.test-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.demo-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.demo-button.start {
  background: linear-gradient(135deg, #8a2be2, #9d4edd);
  color: white;
}

.demo-button.start:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(138, 43, 226, 0.4);
}

.demo-button.start:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.demo-button.clear {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.demo-button.clear:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.demo-button.back {
  background: rgba(108, 117, 125, 0.8);
  color: white;
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
  border: 1px solid rgba(108, 117, 125, 0.3);
}

.demo-button.back:hover {
  background: rgba(108, 117, 125, 1);
  transform: translateY(-1px);
  border-color: rgba(108, 117, 125, 0.5);
}

.test-content {
  flex: 1;
  display: flex;
  padding: 2rem;
}

.test-info {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.03);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.test-info h3 {
  color: #8a2be2;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.test-info ul {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 0.5rem;
}

.test-info li {
  color: #e0e0e0;
  padding: 0.5rem 0;
  font-size: 1rem;
}

@media (max-width: 768px) {
  .test-header {
    padding: 1.5rem 1rem;
  }

  .test-header h1 {
    font-size: 2rem;
  }

  .test-header p {
    font-size: 1rem;
  }

  .test-controls {
    flex-direction: column;
    align-items: center;
  }

  .demo-button {
    width: 200px;
  }

  .test-content {
    padding: 1rem;
  }

  .test-info {
    padding: 1.5rem 1rem;
  }

  .test-info ul {
    grid-template-columns: 1fr;
  }
}

.info-text {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid #8a2be2;
  text-align: left;
  width: 100%;
}

.info-text p {
  margin: 0.5rem 0;
  color: #b0b0b8;
  line-height: 1.6;
}

.error-message {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  text-align: center;
}

/* 右侧字幕显示区域 */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 新的字幕显示容器 - 类似CodePen words动画效果 */
.subtitle-display-new {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(10, 10, 15, 0.95) 0%, rgba(26, 26, 46, 0.95) 50%, rgba(22, 33, 62, 0.95) 100%);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 背景装饰 */
.subtitle-background {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.bg-gradient {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 50% 50%, rgba(138, 43, 226, 0.1) 0%, transparent 70%);
  animation: pulse-bg 4s ease-in-out infinite;
}

.bg-particles {
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.1), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(138, 43, 226, 0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.05), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(138, 43, 226, 0.1), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  animation: float-particles 20s linear infinite;
}

@keyframes pulse-bg {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

@keyframes float-particles {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-100px); }
}

/* 主显示区域 */
.subtitle-main-area {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 900px;
  padding: 2rem;
  min-height: 400px;
  overflow: visible; /* 确保动画不被裁剪 */
}

/* 已确认段落区域 */
.confirmed-segments-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-bottom: 1rem;
  min-height: 80px; /* 减少高度，因为不再移动历史段落 */
  position: relative;
  overflow: visible; /* 确保动画可见 */
}

.animated-segment {
  margin-bottom: 0.3rem; /* 正常的行间距 */
  opacity: 1;
  transform: translateY(0) scale(1);
  transition: none; /* 移除CSS过渡，完全由GSAP控制 */
}

.animated-segment.current-to-history {
  /* 正在从当前段落转换为历史段落的元素 */
  position: relative;
  width: 100%;
  text-align: center;
}

.animated-segment.history {
  /* 已完成转换的历史段落 */
  position: relative;
  width: 100%;
  text-align: center;
}

/* 移除previous和latest的区分，让所有段落看起来一致 */
.animated-segment.previous,
.animated-segment.latest {
  font-size: 1rem; /* 统一字体大小 */
}

.segment-text {
  color: #e0e0e0;
  text-align: center;
  line-height: 1.5;
  font-weight: 400;
  font-size: 1rem;
  padding: 0.3rem 0.5rem; /* 减少内边距，更像文章行 */
  background: none; /* 去掉背景装饰 */
  border-radius: 0; /* 去掉圆角 */
  backdrop-filter: none; /* 去掉毛玻璃效果 */
  border: none; /* 去掉边框 */
  margin-bottom: 0.3rem; /* 正常的行间距 */
}

/* 当前段落区域 - 垂直居中 */
.current-segment-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 120px; /* 固定高度，避免布局变化 */
  min-height: 120px;
}

.typing-text {
  font-size: 1.8rem; /* 稍微减小，与历史段落形成合理层次 */
  font-weight: 600;
  color: #ffffff;
  text-align: center;
  line-height: 1.4;
  margin-bottom: 1rem;
  min-height: 2.5rem;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 0.2rem;
}

.typing-text .word {
  display: inline-block;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.4s ease;
  background: linear-gradient(135deg, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 10px rgba(255, 255, 255, 0.3);
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  gap: 0.3rem;
  align-items: center;
  justify-content: center;
}

.typing-indicator .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: linear-gradient(135deg, #8a2be2, #9d4edd);
  animation: typing-bounce 1.4s infinite ease-in-out;
}

.typing-indicator .dot:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator .dot:nth-child(2) { animation-delay: -0.16s; }
.typing-indicator .dot:nth-child(3) { animation-delay: 0s; }

@keyframes typing-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  opacity: 0.7;
}

.microphone-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: float-icon 3s ease-in-out infinite;
}

.empty-message {
  font-size: 1.5rem;
  color: #e0e0e0;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.empty-hint {
  font-size: 1rem;
  color: #b0b0b8;
  opacity: 0.8;
}

@keyframes float-icon {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* 底部状态栏 */
.subtitle-status-bar {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4ade80;
  animation: pulse-dot 2s infinite;
}

.status-text {
  font-size: 0.9rem;
  color: #e0e0e0;
  font-weight: 500;
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

/* 原有的字幕显示容器 - 保留作为备用 */
.subtitle-display {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.subtitle-container {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* 字幕段落样式 */
.subtitle-segment {
  padding: 1.5rem;
  border-radius: 12px;
  animation: slideInUp 0.5s ease-out;
  transition: all 0.3s ease;
}

.subtitle-segment.confirmed {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #d0d0d0;
}

.subtitle-segment.current {
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.15), rgba(30, 144, 255, 0.15));
  border: 2px solid rgba(0, 122, 255, 0.5);
  color: #ffffff;
  box-shadow: 0 8px 32px rgba(0, 122, 255, 0.2);
}

.subtitle-text {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.subtitle-time {
  font-size: 0.85rem;
  color: #a0a0a0;
  font-weight: 400;
}

.subtitle-status {
  font-size: 0.85rem;
  color: #007AFF;
  font-weight: 400;
  opacity: 0.8;
}

/* 打字光标动画 */
.typing-cursor {
  color: #007AFF;
  animation: blink 1s infinite;
  font-weight: normal;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 空状态样式 */
.subtitle-empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  opacity: 0.6;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.empty-text {
  font-size: 1.1rem;
  color: #a0a0a0;
  font-weight: 400;
}

/* 滑入动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.subtitle-container::-webkit-scrollbar {
  width: 8px;
}

.subtitle-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.subtitle-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.subtitle-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  
  .left-panel {
    flex: 0 0 auto;
    padding: 1rem;
  }
  
  .record-button {
    width: 100px;
    height: 100px;
    font-size: 2rem;
  }
}

/* 新字幕显示的响应式设计 */
@media (max-width: 1024px) {
  .subtitle-main-area {
    padding: 1.5rem;
    min-height: 300px;
  }

  .typing-text {
    font-size: 1.8rem;
  }

  .empty-message {
    font-size: 1.3rem;
  }

  .microphone-icon {
    font-size: 3.5rem;
  }
}

@media (max-width: 768px) {
  .status-bar {
    padding: 1rem;
  }
  
  .main-content {
    padding: 0.5rem;
    gap: 1rem;
  }

  .subtitle-main-area {
    padding: 1rem;
    min-height: 250px;
  }

  .typing-text {
    font-size: 1.5rem;
    min-height: 2rem;
  }

  .empty-message {
    font-size: 1.2rem;
  }

  .empty-hint {
    font-size: 0.9rem;
  }

  .microphone-icon {
    font-size: 3rem;
  }

  .segment-text {
    font-size: 0.9rem;
    padding: 0.2rem 0.4rem; /* 减少移动端的内边距 */
  }

  .animated-segment.previous,
  .animated-segment.latest {
    font-size: 0.9rem; /* 移动端统一字体大小 */
  }

  /* 原有样式保持不变 */
  .subtitle-container {
    padding: 1rem;
  }
  
  .subtitle-text {
    font-size: 1rem;
  }
}
