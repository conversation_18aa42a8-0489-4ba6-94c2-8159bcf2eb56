// WhisperSync 核心类型定义
// 支持 WhisperLiveKit 和原有 whisper-stream 格式

export interface SubtitleSegment {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  isPartial: boolean;  // true=正在识别中，false=已确认
  timestamp: number;   // 接收时间戳
  confidence?: number; // 置信度 (WhisperLiveKit 支持)
  speaker?: string;    // 说话人 (WhisperLiveKit 支持)
}

export interface SubtitleDisplayState {
  confirmedSegments: SubtitleSegment[];  // 已确认的字幕段落
  currentSegment: SubtitleSegment | null;  // 当前正在识别的段落
}

// 原有的 whisper-stream 输出格式
export interface WhisperOutput {
  type: 'WHISPER_RAW_OUTPUT';
  data: {
    message_id: string;
    raw_output: string;
    timestamp: number;
  };
}

// WhisperLiveKit 实际的响应格式
export interface WhisperLiveKitLine {
  speaker: number;
  text: string;
  beg: string;  // 格式: "HH:MM:SS"
  end: string;  // 格式: "HH:MM:SS"
  diff: number; // 与上一个说话人结束时间的差值
}

export interface WhisperLiveKitResponse {
  status: 'active_transcription' | 'no_audio_detected';
  lines: WhisperLiveKitLine[];
  buffer_transcription: string;      // 当前转录缓冲区
  buffer_diarization: string;        // 当前说话人分离缓冲区
  remaining_time_transcription: number; // 转录延迟时间（秒）
  remaining_time_diarization: number;   // 说话人分离延迟时间（秒）
}

export interface WhisperLiveKitStopMessage {
  type: 'ready_to_stop';
}

export interface ConnectionMessage {
  type: 'CONNECTION_SUCCESS';
  message: string;
  timestamp: number;
}

export interface RecordingMessage {
  type: 'RECORDING_STARTED' | 'RECORDING_STOPPED';
  data: {
    timestamp: number;
  };
}

export interface TestMessage {
  type: 'TEST_RESPONSE';
  message: string;
  timestamp: number;
}

export interface ErrorMessage {
  type: 'ERROR';
  message: string;
  timestamp?: number;
}

// 统一的消息类型
export type WebSocketMessage = 
  | WhisperOutput 
  | WhisperLiveKitResponse
  | WhisperLiveKitStopMessage
  | ConnectionMessage 
  | RecordingMessage 
  | TestMessage 
  | ErrorMessage; 