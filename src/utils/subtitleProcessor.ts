import { SubtitleSegment, SubtitleDisplayState } from '../types/transcription';

interface TextFragment {
  text: string;
  startTime: number;
  endTime: number;
  confidence?: number;
}

interface ProcessedSegment {
  id: number;
  startTime: number;
  endTime: number;
  fragments: TextFragment[];
  finalText?: string;
  isConfirmed?: boolean;
}

export class SubtitleProcessor {
  private currentSegmentId: number = 0;
  private isInTranscription: boolean = false;
  private onUpdate: (state: SubtitleDisplayState) => void;
  private displayState: SubtitleDisplayState = {
    confirmedSegments: [],
    currentSegment: null
  };
  
  // 新增：用于处理重复和修正的数据结构
  private processedSegments: Map<number, ProcessedSegment> = new Map();
  private lastConfirmedSegmentId: number = -1;
  private processedMessages: Set<string> = new Set();

  constructor(onUpdate: (state: SubtitleDisplayState) => void) {
    this.onUpdate = onUpdate;
  }

  /**
   * 处理whisper-stream原始输出
   */
  processRawOutput(rawOutput: string): void {
    const messageHash = this.generateMessageHash(rawOutput);
    if (this.processedMessages.has(messageHash)) {
      console.log(`🔄 跳过重复消息: ${messageHash}`);
      return;
    }
    this.processedMessages.add(messageHash);

    console.log(`🔄 处理新消息 (${rawOutput.length}字符)`);
    const lines = rawOutput.split('\n');
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;
      
      const startMatch = trimmedLine.match(/###\s*Transcription\s+(\d+)\s+START\s*\|\s*t0\s*=\s*(\d+)ms\s*\|\s*t1\s*=\s*(\d+)ms/);
      if (startMatch && startMatch.length >= 4) {
        const segmentId = parseInt(startMatch[1]!);
        const startTime = parseInt(startMatch[2]!);
        const endTime = parseInt(startMatch[3]!);
        console.log(`🎬 段落 ${segmentId} 开始`);
        this.handleTranscriptionStart(segmentId, startTime, endTime);
        continue;
      } else {
        if (trimmedLine.includes('Transcription') && trimmedLine.includes('START')) {
          const simpleMatch = trimmedLine.match(/Transcription\s+(\d+)\s+START/);
          if (simpleMatch && simpleMatch[1]) {
            const segmentId = parseInt(simpleMatch[1]);
            this.handleTranscriptionStart(segmentId, 0, 0);
            continue;
          }
        }
      }

      const endMatch = trimmedLine.match(/### Transcription (\d+) END/);
      if (endMatch && endMatch[1]) {
        const segmentId = parseInt(endMatch[1]);
        console.log(`🏁 段落 ${segmentId} 结束`);
        this.handleTranscriptionEnd(segmentId);
        continue;
      }

      if (this.isInTranscription) {
        const textMatch = trimmedLine.match(/\[(\d{2}:\d{2}:\d{2}\.\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2}\.\d{3})\]\s*(.+)/);
        if (textMatch && textMatch.length >= 4) {
          const startTimeStr = textMatch[1]!;
          const endTimeStr = textMatch[2]!;
          const text = textMatch[3]!.trim();
          
          const startTime = this.parseTimeString(startTimeStr);
          const endTime = this.parseTimeString(endTimeStr);
          
          if (this.isValidText(text)) {
            this.addTextFragment(text, startTime, endTime);
          }
        }
      }
    }
  }

  private generateMessageHash(rawOutput: string): string {
    const segmentMatch = rawOutput.match(/Transcription\s+(\d+)\s+START.*?t0\s*=\s*(\d+)ms/);
    if (segmentMatch) {
      return `${segmentMatch[1]}-${segmentMatch[2]}`;
    }
    return rawOutput.slice(0, 100);
  }

  private parseTimeString(timeStr: string): number {
    const parts = timeStr.split(':');
    if (parts.length !== 3) return 0;
    
    const hours = parseInt(parts[0]!) || 0;
    const minutes = parseInt(parts[1]!) || 0;
    const secondsParts = parts[2]!.split('.');
    if (secondsParts.length !== 2) return 0;
    
    const seconds = parseInt(secondsParts[0]!) || 0;
    const milliseconds = parseInt(secondsParts[1]!) || 0;
    
    return hours * 3600000 + minutes * 60000 + seconds * 1000 + milliseconds;
  }

  private handleTranscriptionStart(segmentId: number, startTime: number, endTime: number): void {
    this.currentSegmentId = segmentId;
    this.isInTranscription = true;
    
    const existingSegment = this.processedSegments.get(segmentId);
    if (existingSegment?.isConfirmed) {
      console.log(`⏭️ 跳过已确认段落 ${segmentId}`);
      return;
    }
    
    if (!this.processedSegments.has(segmentId)) {
      this.processedSegments.set(segmentId, {
        id: segmentId,
        startTime,
        endTime,
        fragments: [],
        isConfirmed: false
      });
    } else {
      const segment = this.processedSegments.get(segmentId)!;
      segment.fragments = [];
      segment.startTime = startTime;
      segment.endTime = endTime;
      console.log(`🔄 重新处理段落 ${segmentId}`);
    }
  }

  private handleTranscriptionEnd(segmentId: number): void {
    const segment = this.processedSegments.get(segmentId);
    if (!segment || segment.isConfirmed) {
      return;
    }
    
    if (segment.fragments.length > 0) {
      this.processSegmentFragments(segmentId);
      
      this.updateDisplay();
    }
    
    this.isInTranscription = false;
  }

  private addTextFragment(text: string, startTime: number, endTime: number): void {
    const segment = this.processedSegments.get(this.currentSegmentId);
    if (!segment || segment.isConfirmed) return;

    const fragment: TextFragment = {
      text,
      startTime,
      endTime
    };

    segment.fragments.push(fragment);
    
    this.updateCurrentSegmentDisplay();
  }

  private processSegmentFragments(segmentId: number): void {
    const segment = this.processedSegments.get(segmentId);
    if (!segment) return;

    const deduplicatedText = this.deduplicateFragments(segment.fragments);
    segment.finalText = deduplicatedText;
    
    console.log(`✅ 段落 ${segmentId} 完成: "${deduplicatedText}"`);
  }

  private deduplicateFragments(fragments: TextFragment[]): string {
    if (fragments.length === 0) return '';
    
    const sortedFragments = [...fragments].sort((a, b) => a.startTime - b.startTime);
    
    const mergedTexts: string[] = [];
    let currentText = '';
    
    for (const fragment of sortedFragments) {
      const text = fragment.text.trim();
      
      if (currentText && this.hasTextOverlap(currentText, text)) {
        if (text.length > currentText.length) {
          currentText = text;
        }
      } else {
        if (currentText) {
          mergedTexts.push(currentText);
        }
        currentText = text;
      }
    }
    
    if (currentText) {
      mergedTexts.push(currentText);
    }
    
    return mergedTexts.join(' ').trim();
  }

  private hasTextOverlap(text1: string, text2: string): boolean {
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);
    
    // 如果两个文本长度差异很大，不认为是重复（可能是扩展内容）
    const lengthRatio = Math.min(words1.length, words2.length) / Math.max(words1.length, words2.length);
    if (lengthRatio < 0.7) {
      return false; // 长度差异超过30%，认为是新内容
    }
    
    // 如果一个文本完全包含在另一个中，且长度相近，认为有重叠
    if (text1.toLowerCase().includes(text2.toLowerCase()) || 
        text2.toLowerCase().includes(text1.toLowerCase())) {
      return true;
    }
    
    // 检查是否有连续的词语匹配（至少5个词，提高阈值）
    const minWords = Math.min(words1.length, words2.length);
    const overlapThreshold = Math.max(5, Math.floor(minWords * 0.8)); // 至少5个词或80%的重叠
    
    for (let i = 0; i <= words1.length - overlapThreshold; i++) {
      const sequence1 = words1.slice(i, i + overlapThreshold).join(' ');
      if (text2.toLowerCase().includes(sequence1)) {
        return true;
      }
    }
    
    return false;
  }

  private updateCurrentSegmentDisplay(): void {
    const segment = this.processedSegments.get(this.currentSegmentId);
    if (!segment || segment.isConfirmed) return;

    const currentText = this.deduplicateFragments(segment.fragments);
    
    this.displayState.currentSegment = {
      id: `segment-${this.currentSegmentId}`,
      text: currentText,
      startTime: segment.startTime,
      endTime: segment.endTime,
      isPartial: true,
      timestamp: Date.now()
    };

    this.notifyUpdate();
  }

  private updateDisplay(): void {
    const newConfirmedSegments: SubtitleSegment[] = [];
    
    const sortedSegments = Array.from(this.processedSegments.entries())
      .sort(([a], [b]) => a - b);
    
    for (const [segmentId, segment] of sortedSegments) {
      if (segment.finalText && !segment.isConfirmed && segmentId > this.lastConfirmedSegmentId) {
        // 改进的去重检查：只检查是否与最近的段落重复
        const recentSegments = this.displayState.confirmedSegments.slice(-2); // 只检查最近2个段落
        const isDuplicate = recentSegments.some(existing => 
          this.hasTextOverlap(existing.text, segment.finalText!)
        );
        
        // 额外检查：如果新内容明显更长，即使有重叠也不跳过
        const hasSignificantNewContent = recentSegments.length === 0 || 
          recentSegments.every(existing => segment.finalText!.length > existing.text.length * 1.3);
        
        if (!isDuplicate || hasSignificantNewContent) {
          newConfirmedSegments.push({
            id: `segment-${segmentId}`,
            text: segment.finalText,
            startTime: segment.startTime,
            endTime: segment.endTime,
            isPartial: false,
            timestamp: Date.now()
          });
          
          segment.isConfirmed = true;
          this.lastConfirmedSegmentId = segmentId;
          
          console.log(`✅ 新增确认段落 ${segmentId} (${segment.finalText.length}字符): "${segment.finalText.slice(0, 50)}..."`);
        } else {
          console.log(`🚫 跳过重复段落 ${segmentId} (${segment.finalText.length}字符)`);
          segment.isConfirmed = true;
        }
      }
    }
    
    this.displayState.confirmedSegments.push(...newConfirmedSegments);
    
    if (this.displayState.confirmedSegments.length > 10) {
      const excess = this.displayState.confirmedSegments.length - 10;
      this.displayState.confirmedSegments.splice(0, excess);
    }
    
    this.displayState.currentSegment = null;
    
    this.notifyUpdate();
  }

  private isValidText(text: string): boolean {
    if (!text || text.trim() === '') return false;
    
    const invalidPatterns = [
      'silence', 'background noise', 'music', 'applause',
      'laughter', 'coughing', 'breathing', 'keyboard clicking'
    ];
    
    const lowerText = text.toLowerCase();
    return !invalidPatterns.some(pattern => lowerText.includes(pattern));
  }

  private notifyUpdate(): void {
    this.onUpdate({ ...this.displayState });
  }

  /**
   * 添加确认的段落（用于WhisperLiveKit）
   */
  addConfirmedSegment(segment: {
    text: string;
    speaker: number;
    timestamp: number;
    confidence?: number;
  }): void {
    if (!segment.text.trim()) return;

    // 检查是否与最近的段落重复
    const recentSegments = this.displayState.confirmedSegments.slice(-2);
    const isDuplicate = recentSegments.some(existing =>
      this.hasTextOverlap(existing.text, segment.text)
    );

    if (!isDuplicate) {
      const newSegment: SubtitleSegment = {
        id: `whisperlivekit-${Date.now()}-${Math.random()}`,
        text: segment.text,
        startTime: 0, // WhisperLiveKit不提供精确时间
        endTime: 0,
        isPartial: false,
        timestamp: segment.timestamp,
        speaker: segment.speaker,
        confidence: segment.confidence
      };

      this.displayState.confirmedSegments.push(newSegment);

      // 限制显示的段落数量
      if (this.displayState.confirmedSegments.length > 10) {
        this.displayState.confirmedSegments.shift();
      }

      console.log(`✅ 添加确认段落 [说话人${segment.speaker}]: "${segment.text.slice(0, 50)}..."`);
      this.notifyUpdate();
    } else {
      console.log(`🚫 跳过重复段落 [说话人${segment.speaker}]: "${segment.text.slice(0, 30)}..."`);
    }
  }

  /**
   * 更新当前实时段落（用于WhisperLiveKit）
   */
  updateCurrentSegment(segment: {
    text: string;
    speaker: number;
    timestamp: number;
    confidence?: number;
  }): void {
    if (!segment.text.trim()) {
      this.displayState.currentSegment = null;
    } else {
      this.displayState.currentSegment = {
        id: `current-whisperlivekit-${segment.speaker}`,
        text: segment.text,
        startTime: 0,
        endTime: 0,
        isPartial: true,
        timestamp: segment.timestamp,
        speaker: segment.speaker,
        confidence: segment.confidence
      };
    }

    this.notifyUpdate();
  }

  clear(): void {
    this.displayState = {
      confirmedSegments: [],
      currentSegment: null
    };
    this.processedSegments.clear();
    this.processedMessages.clear();
    this.lastConfirmedSegmentId = -1;
    this.isInTranscription = false;
    this.notifyUpdate();
  }
}