import { useState, useCallback, useRef, useEffect } from 'react';
import './App.css';
import SubtitleDisplay from './components/SubtitleDisplay';
import SubtitleTestPage from './components/SubtitleTestPage';
import { SubtitleProcessor } from './utils/subtitleProcessor';
import { SubtitleDisplayState, WebSocketMessage } from './types/transcription';

function App() {
  const [isRecording, setIsRecording] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState('连接中...');
  const [displayState, setDisplayState] = useState<SubtitleDisplayState>({
    confirmedSegments: [],
    currentSegment: null
  });
  const [showTestPage, setShowTestPage] = useState(false);
  const [serverInfo, setServerInfo] = useState<string>('');

  const wsRef = useRef<WebSocket | null>(null);
  const subtitleProcessorRef = useRef<SubtitleProcessor | null>(null);

  // 处理 WhisperLiveKit 原生响应格式
  const handleWhisperLiveKitResponse = useCallback((response: import('./types/transcription').WhisperLiveKitResponse) => {
    if (!subtitleProcessorRef.current) {
      console.error('❌ SubtitleProcessor未初始化！');
      return;
    }

    // 将 WhisperLiveKit 格式转换为我们的内部格式
    if (response.status === 'no_audio_detected') {
      console.log('🔇 未检测到音频');
      return;
    }

    // 处理已确认的行
    response.lines.forEach((line) => {
      if (line.text.trim()) {
        // 创建模拟的 whisper-stream 输出格式
        const mockOutput = `[${line.beg} --> ${line.end}] Speaker ${line.speaker}: ${line.text}`;
        subtitleProcessorRef.current!.processRawOutput(mockOutput);
      }
    });

    // 处理缓冲区内容（正在处理的文本）
    if (response.buffer_transcription || response.buffer_diarization) {
      const bufferText = [response.buffer_diarization, response.buffer_transcription]
        .filter(Boolean)
        .join(' ');
      
      if (bufferText.trim()) {
        // 创建临时的部分结果
        const currentTime = new Date().toISOString().substr(11, 8);
        const mockPartialOutput = `[${currentTime} --> ${currentTime}] Processing: ${bufferText}`;
        subtitleProcessorRef.current!.processRawOutput(mockPartialOutput);
      }
    }
  }, []);

  // WebSocket连接
  const connectWebSocket = useCallback(() => {
    console.log('🔗 开始连接 WhisperLiveKit WebSocket');
    
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      console.log('✅ WebSocket已连接，跳过重复连接');
      return;
    }
    
    if (wsRef.current) {
      wsRef.current.close();
    }

    const wsUrl = 'ws://localhost:8889/ws';
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log('✅ WhisperLiveKit WebSocket连接成功');
      setConnectionStatus('已连接 (WhisperLiveKit)');
      setError(null);
      
      // 发送连接确认
      ws.send(JSON.stringify({ 
        type: 'CLIENT_CONNECTED', 
        timestamp: Date.now(),
        userAgent: navigator.userAgent.substring(0, 50)
      }));
    };

    ws.onmessage = (event) => {
      try {
        const rawMessage = JSON.parse(event.data);

        // 检查是否为转换后的 WhisperLiveKit 格式
        if ('type' in rawMessage && rawMessage.type === 'WHISPERLIVEKIT_RESULT') {
          console.log('📥 收到转换后的 WhisperLiveKit 数据:', rawMessage);

          // 处理确认的段落
          if (rawMessage.confirmed_segments && Array.isArray(rawMessage.confirmed_segments)) {
            rawMessage.confirmed_segments.forEach((segment: {
              text: string;
              speaker: number;
              confidence?: number;
            }) => {
              if (subtitleProcessorRef.current && segment.text.trim()) {
                console.log(`📝 处理确认段落 [说话人${segment.speaker}]: ${segment.text.slice(0, 30)}...`);
                subtitleProcessorRef.current.addConfirmedSegment({
                  text: segment.text,
                  speaker: segment.speaker,
                  timestamp: Date.now(),
                  confidence: segment.confidence || 0.9
                });
              }
            });
          }

          // 处理当前实时段落
          if (rawMessage.current_segment && subtitleProcessorRef.current) {
            const currentSeg = rawMessage.current_segment as {
              text: string;
              speaker: number;
              confidence?: number;
            };
            if (currentSeg.text.trim()) {
              console.log(`🔄 处理实时段落 [说话人${currentSeg.speaker}]: ${currentSeg.text.slice(0, 30)}...`);
              subtitleProcessorRef.current.updateCurrentSegment({
                text: currentSeg.text,
                speaker: currentSeg.speaker,
                timestamp: Date.now(),
                confidence: currentSeg.confidence || 0.7
              });
            }
          }

          return;
        }

        // 首先检查是否为 WhisperLiveKit 的原生响应格式（向后兼容）
        if ('status' in rawMessage && 'lines' in rawMessage) {
          console.log('📥 收到 WhisperLiveKit 原生数据:', rawMessage);
          handleWhisperLiveKitResponse(rawMessage);
          return;
        }

        // 检查是否为停止信号
        if ('type' in rawMessage && rawMessage.type === 'ready_to_stop') {
          console.log('🏁 WhisperLiveKit 处理完成');
          setIsRecording(false);
          return;
        }

        // 处理其他类型的消息
        const message = rawMessage as WebSocketMessage;
        if (!('type' in message)) {
          console.log('📨 收到未知消息格式:', rawMessage);
          return;
        }

        switch (message.type) {
          case 'CONNECTION_SUCCESS':
            console.log('🎉 WhisperLiveKit 连接成功:', message.message);
            setServerInfo(message.message);
            setError(`连接成功 - ${new Date(message.timestamp).toLocaleTimeString()}`);
            setTimeout(() => setError(null), 3000);
            break;

          case 'WHISPER_RAW_OUTPUT':
            // 兼容原有的 whisper-stream 格式
            console.log('📥 收到转录数据:', message.data.raw_output.slice(0, 100) + '...');
            if (subtitleProcessorRef.current) {
              subtitleProcessorRef.current.processRawOutput(message.data.raw_output);
            } else {
              console.error('❌ SubtitleProcessor未初始化！');
            }
            break;

          case 'RECORDING_STARTED':
            console.log('🎤 录音开始 (WhisperLiveKit)');
            setIsRecording(true);
            setError(null);
            if (subtitleProcessorRef.current) {
              subtitleProcessorRef.current.clear();
            }
            break;

          case 'RECORDING_STOPPED':
            console.log('⏹️ 录音停止 (WhisperLiveKit)');
            setIsRecording(false);
            break;

          case 'TEST_RESPONSE':
            console.log('🧪 连接测试成功:', message);
            setError(`连接测试成功 - ${new Date(message.timestamp).toLocaleTimeString()}`);
            setTimeout(() => setError(null), 3000);
            break;

          case 'ERROR':
            console.error('❌ 收到错误消息:', message);
            setError(message.message || '识别时发生错误');
            break;

          default:
            console.log('📨 收到未知消息类型:', message);
        }
      } catch (err) {
        console.error('❌ 解析WebSocket消息失败:', err);
        setError('消息解析失败');
      }
    };

    ws.onerror = (error) => {
      console.error('❌ WhisperLiveKit WebSocket错误:', error);
      setConnectionStatus('连接错误');
      setError('WebSocket连接错误，请检查 WhisperLiveKit 服务');
    };

    ws.onclose = (event) => {
      console.log('🔌 WhisperLiveKit WebSocket断开连接:', event.code, event.reason);
      setConnectionStatus('连接断开');
      wsRef.current = null;
      
      // 自动重连（非正常关闭时）
      if (event.code !== 1000 && event.code !== 1001) {
        setTimeout(() => {
          if (!wsRef.current || wsRef.current.readyState === WebSocket.CLOSED) {
            console.log('🔄 尝试重新连接 WhisperLiveKit...');
            connectWebSocket();
          }
        }, 3000);
      }
    };

    wsRef.current = ws;
  }, []);

  // 初始化字幕处理器
  useEffect(() => {
    console.log('🚀 初始化字幕处理器 (WhisperLiveKit 兼容)');
    subtitleProcessorRef.current = new SubtitleProcessor((newState) => {
      console.log('📺 字幕状态更新:', {
        confirmedCount: newState.confirmedSegments.length,
        currentText: newState.currentSegment?.text?.slice(0, 30) || 'none'
      });
      setDisplayState(newState);
    });
  }, []);

  // 初始化WebSocket连接
  useEffect(() => {
    connectWebSocket();
    return () => {
      // 清理WebSocket连接
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }

      // 清理音频资源
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        mediaRecorderRef.current.stop();
      }

      if (audioStreamRef.current) {
        audioStreamRef.current.getTracks().forEach(track => track.stop());
        audioStreamRef.current = null;
      }
    };
  }, [connectWebSocket]);

  // 音频相关状态
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioStreamRef = useRef<MediaStream | null>(null);

  // 开始录音
  const handleStartRecording = async () => {
    console.log('🎤 请求开始录音 (WhisperLiveKit)');
    setError(null);

    if (wsRef.current?.readyState !== WebSocket.OPEN) {
      setError('WebSocket未连接，无法开始识别');
      return;
    }

    try {
      // 请求麦克风权限
      console.log('🎤 请求麦克风权限...');
      const audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      });

      console.log('✅ 麦克风权限获取成功');
      audioStreamRef.current = audioStream;

      // 创建 MediaRecorder
      const mediaRecorder = new MediaRecorder(audioStream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0 && wsRef.current?.readyState === WebSocket.OPEN) {
          wsRef.current.send(event.data);
          console.log(`📤 发送音频数据: ${event.data.size} 字节`);
        }
      };

      mediaRecorder.onstart = () => {
        console.log('🔴 录音开始');
        setIsRecording(true);
        // 发送开始录音的JSON消息
        if (wsRef.current?.readyState === WebSocket.OPEN) {
          wsRef.current.send(JSON.stringify({ type: 'START_RECORDING' }));
        }
      };

      mediaRecorder.onstop = () => {
        console.log('⏹️ 录音停止');
        setIsRecording(false);
        // 发送停止录音的JSON消息
        if (wsRef.current?.readyState === WebSocket.OPEN) {
          wsRef.current.send(JSON.stringify({ type: 'STOP_RECORDING' }));
        }
      };

      // 开始录音，每 500ms 发送一次数据
      mediaRecorder.start(500);

    } catch (error) {
      console.error('❌ 录音启动失败:', error);
      setError(`录音启动失败: ${(error as Error).message}`);
    }
  };

  // 停止录音
  const handleStopRecording = () => {
    console.log('⏹️ 请求停止录音 (WhisperLiveKit)');

    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
    }

    if (audioStreamRef.current) {
      audioStreamRef.current.getTracks().forEach(track => track.stop());
      audioStreamRef.current = null;
    }
  };

  // 清空结果
  const handleClearResults = () => {
    console.log('🧹 清空字幕结果');
    if (subtitleProcessorRef.current) {
      subtitleProcessorRef.current.clear();
    }
  };

  // 测试连接
  const handleTestConnection = () => {
    console.log('🧪 测试 WhisperLiveKit WebSocket连接');
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({ 
        type: 'TEST', 
        data: '连接测试',
        timestamp: Date.now()
      }));
    } else {
      setError('WebSocket未连接，无法测试');
    }
  };

  // 如果显示测试页面，直接返回测试页面
  if (showTestPage) {
    return <SubtitleTestPage onBackToMain={() => setShowTestPage(false)} />;
  }

  return (
    <div className="app-container">
      {/* 状态栏 */}
      <div className="status-bar">
        <div className="connection-status">
          状态: {connectionStatus}
          {serverInfo && <span className="server-info"> | {serverInfo}</span>}
        </div>
        <div className="status-controls">
          <button 
            onClick={() => setShowTestPage(true)}
            className="test-button"
          >
            动画演示
          </button>
          <button 
            onClick={handleTestConnection}
            className="test-button"
            disabled={connectionStatus !== '已连接 (WhisperLiveKit)'}
          >
            测试连接
          </button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {/* 主要内容区域 */}
      <div className="main-content">
        {/* 控制面板 */}
        <div className="control-panel">
          <div className="recording-controls">
            <button
              onClick={isRecording ? handleStopRecording : handleStartRecording}
              className={`record-button ${isRecording ? 'recording' : ''}`}
              disabled={connectionStatus !== '已连接 (WhisperLiveKit)'}
            >
              {isRecording ? '⏹️ 停止识别' : '🎤 开始识别'}
            </button>
            
            <button
              onClick={handleClearResults}
              className="clear-button"
              disabled={displayState.confirmedSegments.length === 0 && !displayState.currentSegment}
            >
              🧹 清空结果
            </button>
          </div>

          <div className="status-info">
            <div className="recording-status">
              {isRecording ? (
                <span className="recording-indicator">🔴 正在识别...</span>
              ) : (
                <span className="idle-indicator">⚪ 待机中</span>
              )}
            </div>
            
            <div className="segment-count">
              已识别段落: {displayState.confirmedSegments.length}
            </div>
          </div>
        </div>

        {/* 字幕显示区域 */}
        <div className="subtitle-container">
          <SubtitleDisplay 
            displayState={displayState}
            isRecording={isRecording}
          />
        </div>
      </div>

      {/* 底部信息 */}
      <div className="footer-info">
        <span>WhisperSync v2.0 - 基于 WhisperLiveKit</span>
        <span>实时语音识别 | 本地处理 | 隐私保护</span>
      </div>
    </div>
  );
}

export default App;