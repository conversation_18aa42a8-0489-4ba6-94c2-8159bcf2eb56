/**
 * 全局类型声明
 */

export interface ElectronAPI {
  // 基础通信
  ping: () => Promise<string>;
  
  // 录音控制
  startRecording: () => Promise<{ success: boolean }>;
  stopRecording: () => Promise<{ success: boolean }>;
  
  // 识别结果
  getFinalResult: () => Promise<string>;
  resetRecognizer: () => Promise<boolean>;
  
  // 语言设置
  setRecognitionLanguage: (language: string) => Promise<boolean>;
  setTranslationLanguages: (source: string, target: string) => Promise<boolean>;
  translate: (text: string) => Promise<string>;
  
  // 引擎管理
  setEngine: (engineType: string, backend: string) => Promise<boolean>;
  getEngineConfig: () => Promise<{
    engineType: string;
    backend: string;
    language: string;
    model: string;
  }>;
  getAvailableBackends: () => Promise<Array<{
    id: string;
    name: string;
  }>>;
  checkConnection: () => Promise<boolean>;
  getConnectionStatus: () => Promise<{ connected: boolean; url?: string }>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};
