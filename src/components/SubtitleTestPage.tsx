import React, { useState } from 'react';
import SubtitleDisplay from './SubtitleDisplay';
import { SubtitleDisplayState } from '../types/transcription';

interface SubtitleTestPageProps {
  onBackToMain?: () => void;
}

const SubtitleTestPage: React.FC<SubtitleTestPageProps> = ({ onBackToMain }) => {
  const [displayState, setDisplayState] = useState<SubtitleDisplayState>({
    confirmedSegments: [],
    currentSegment: null
  });

  const [isDemo, setIsDemo] = useState(false);

  // 模拟字幕数据
  const demoSegments = [
    "欢迎使用WhisperSync实时语音转录系统",
    "这是一个基于whisper.cpp的实时语音识别应用",
    "支持中文和英文语音识别",
    "具有美观的动画效果和用户界面",
    "类似CodePen中words动画的显示效果",
    "每个单词都会逐个出现，带有流畅的动画",
    "新的句子会从下方滑入，旧的句子向上移动",
    "这样的设计让用户体验更加优雅和现代"
  ];

  const demoWords = [
    ["欢迎", "使用", "WhisperSync", "实时", "语音", "转录", "系统"],
    ["这是", "一个", "基于", "whisper.cpp", "的", "实时", "语音", "识别", "应用"],
    ["支持", "中文", "和", "英文", "语音", "识别"],
    ["具有", "美观的", "动画", "效果", "和", "用户", "界面"],
    ["类似", "CodePen", "中", "words", "动画的", "显示", "效果"],
    ["每个", "单词", "都会", "逐个", "出现", "带有", "流畅的", "动画"],
    ["新的", "句子", "会从", "下方", "滑入", "旧的", "句子", "向上", "移动"],
    ["这样的", "设计", "让", "用户", "体验", "更加", "优雅", "和", "现代"]
  ];

  const startDemo = () => {
    setIsDemo(true);
    setDisplayState({
      confirmedSegments: [],
      currentSegment: null
    });

    let segmentIndex = 0;
    let wordIndex = 0;

    const processNextSegment = () => {
      if (segmentIndex >= demoSegments.length) {
        setIsDemo(false);
        return;
      }

      const currentWords = demoWords[segmentIndex];
      if (!currentWords) return;
      
      wordIndex = 0;

      // 开始新段落
      const addWord = () => {
        if (wordIndex < currentWords.length) {
          const currentText = currentWords.slice(0, wordIndex + 1).join(' ');
          
          setDisplayState(prev => ({
            ...prev,
            currentSegment: {
              id: `demo-${segmentIndex}`,
              text: currentText,
              startTime: Date.now(),
              endTime: Date.now() + 3000,
              isPartial: true,
              timestamp: Date.now()
            }
          }));

          wordIndex++;
          setTimeout(addWord, 200 + Math.random() * 150); // 稍微加快速度，更好展示效果
        } else {
          // 段落完成，先添加到确认段落，但保持currentSegment不变，让动画执行
          setTimeout(() => {
            setDisplayState(prev => ({
              confirmedSegments: [
                ...prev.confirmedSegments,
                {
                  id: `demo-${segmentIndex}`,
                  text: demoSegments[segmentIndex] || '',
                  startTime: Date.now() - 3000,
                  endTime: Date.now(),
                  isPartial: false,
                  timestamp: Date.now()
                }
              ],
              currentSegment: prev.currentSegment // 保持当前段落，让动画执行
            }));

            // 等待动画完成后再清空currentSegment并开始下一个段落
            setTimeout(() => {
              setDisplayState(prev => ({
                ...prev,
                currentSegment: null
              }));
              
              segmentIndex++;
              setTimeout(processNextSegment, 500); // 段落间隔
            }, 1200); // 等待动画完成（动画时长0.8s + 缓冲时间）
          }, 500);
        }
      };

      addWord();
    };

    processNextSegment();
  };

  const clearDemo = () => {
    setIsDemo(false);
    setDisplayState({
      confirmedSegments: [],
      currentSegment: null
    });
  };

  return (
    <div className="subtitle-test-page">
      <div className="test-header">
        <div className="header-top">
          <button 
            onClick={onBackToMain || (() => window.location.reload())}
            className="demo-button back"
          >
            ← 返回主页
          </button>
        </div>
        
        <h1>WhisperSync 字幕显示测试</h1>
        <p>类似CodePen words动画效果的实时字幕显示</p>
        
        <div className="test-controls">
          <button 
            onClick={startDemo} 
            disabled={isDemo}
            className="demo-button start"
          >
            {isDemo ? '演示进行中...' : '开始演示'}
          </button>
          <button 
            onClick={clearDemo}
            className="demo-button clear"
          >
            清除内容
          </button>
        </div>
      </div>

      <div className="test-content">
        <SubtitleDisplay displayState={displayState} />
      </div>

      <div className="test-info">
        <h3>功能特点：</h3>
        <ul>
          <li>✨ 垂直居中的字幕显示</li>
          <li>🎬 GSAP动画效果，单词逐个显示</li>
          <li>📱 响应式设计，适配各种屏幕</li>
          <li>🎨 美观的背景和粒子效果</li>
          <li>⬆️ 新句子时，旧内容向上移动</li>
          <li>💫 流畅的过渡动画</li>
        </ul>
      </div>
    </div>
  );
};

export default SubtitleTestPage; 