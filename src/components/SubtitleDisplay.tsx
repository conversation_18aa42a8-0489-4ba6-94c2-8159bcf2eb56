import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { SubtitleDisplayState } from '../types/transcription';

interface SubtitleDisplayProps {
  displayState: SubtitleDisplayState;
  isRecording?: boolean; // 可选的录音状态，用于显示录音指示器
}

interface AnimatedSegment {
  id: string;
  text: string;
  timestamp: number;
  isConfirmed: boolean;
}

const SubtitleDisplay: React.FC<SubtitleDisplayProps> = ({ displayState, isRecording = false }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const currentSegmentRef = useRef<HTMLDivElement>(null);
  const confirmedSegmentsRef = useRef<HTMLDivElement>(null);
  const [animatedSegments, setAnimatedSegments] = useState<AnimatedSegment[]>([]);

  // 处理新的确认段落
  useEffect(() => {
    if (displayState.confirmedSegments.length > 0) {
      const latestSegment = displayState.confirmedSegments[displayState.confirmedSegments.length - 1];
      
      if (latestSegment) {
        setAnimatedSegments(prev => {
          const exists = prev.find(seg => seg.id === latestSegment.id);
          if (!exists) {
            // 直接使用确认段落的文本触发动画
            console.log('触发上移动画，文本:', latestSegment.text);
            animateSegmentTransition(latestSegment.text);
            
            // 只保留最近的3个段落
            return prev.slice(-2); // 为新段落留出空间
          }
          return prev;
        });
      }
    }
  }, [displayState.confirmedSegments]);

  // 动画：段落向上移动 - 从当前位置直接开始移动
  const animateSegmentTransition = (completedText: string) => {
    if (!currentSegmentRef.current || !confirmedSegmentsRef.current) return;

    console.log('开始执行动画，文本:', completedText);

    const currentElement = currentSegmentRef.current.querySelector('.typing-text');
    if (!currentElement) return;

    // 获取所有现有的历史段落
    const existingSegments = confirmedSegmentsRef.current.querySelectorAll('.animated-segment');
    console.log('现有段落数量:', existingSegments.length);

    // 创建动画时间线
    const tl = gsap.timeline({
      onStart: () => console.log('动画开始'),
      onComplete: () => console.log('动画完成')
    });
    
    // 只移动当前段落，历史段落保持不动，这样更自然
    // 当前段落从原位置开始向上移动，同时轻微调整样式
    console.log('移动当前段落');
    tl.to(currentElement, {
      y: -30, // 减少移动距离
      opacity: 0.7, // 轻微降低透明度
      scale: 0.9, // 轻微缩小
      duration: 0.8,
      ease: "power2.out",
      onComplete: () => {
        // 动画完成后，将当前段落转移到历史区域
        const newSegment = document.createElement('div');
        newSegment.className = 'animated-segment history';
        newSegment.innerHTML = `<div class="segment-text">${completedText}</div>`;
        
        // 设置最终的历史样式
        const segmentText = newSegment.querySelector('.segment-text');
        if (segmentText) {
          gsap.set(segmentText, {
            fontSize: '1.6rem', // 保持相对较大的字体
            fontWeight: 500, // 稍微减轻字重
            color: '#d0d0d0', // 稍微降低亮度
            opacity: 0.7,
            scale: 0.9,
            y: 0 // 重置y位置，避免累积位移
          });
        }
        
        if (confirmedSegmentsRef.current) {
          confirmedSegmentsRef.current.appendChild(newSegment);
        }
        
        // 不完全清空，保持一个透明占位符以维持布局
        currentElement.innerHTML = '<span style="opacity: 0; height: 2.5rem; display: block;">&nbsp;</span>';
        gsap.set(currentElement, { y: 0, opacity: 1, scale: 1 }); // 重置当前区域样式
        
        console.log('段落转换完成');
      }
    }, 0);
  };

  // 当前段落的打字机效果 - 优化版本，避免闪烁
  useEffect(() => {
    if (displayState.currentSegment && currentSegmentRef.current) {
      const text = displayState.currentSegment.text;
      const element = currentSegmentRef.current.querySelector('.typing-text');
      
      if (element && text) {
        const words = text.split(' ');
        const currentWords = Array.from(element.querySelectorAll('.word'));
        
        // 如果当前单词数量少于新文本的单词数量，说明有新单词需要添加
        if (currentWords.length < words.length) {
          // 只添加新的单词，不重新渲染整行
          for (let i = currentWords.length; i < words.length; i++) {
            const wordSpan = document.createElement('span');
            wordSpan.className = 'word';
            wordSpan.textContent = words[i] + (i < words.length - 1 ? ' ' : '');
            element.appendChild(wordSpan);
            
            // 立即设置初始状态并动画显示
            gsap.set(wordSpan, { opacity: 0, y: 20 });
            gsap.to(wordSpan, {
              opacity: 1,
              y: 0,
              duration: 0.4,
              ease: "power2.out",
              delay: 0.05 * (i - currentWords.length) // 轻微的延迟让动画更自然
            });
          }
        } else if (currentWords.length === 0 || element.innerHTML.includes('&nbsp;')) {
          // 如果是全新的段落或包含占位符，一次性创建所有单词
          element.innerHTML = '';
          words.forEach((word, index) => {
            const wordSpan = document.createElement('span');
            wordSpan.className = 'word';
            wordSpan.textContent = word + (index < words.length - 1 ? ' ' : '');
            element.appendChild(wordSpan);
          });

          // 动画显示所有单词
          const wordElements = element.querySelectorAll('.word');
          gsap.set(wordElements, { opacity: 0, y: 20 });
          gsap.to(wordElements, {
            opacity: 1,
            y: 0,
            duration: 0.4,
            ease: "power2.out",
            stagger: 0.08
          });
        }
      }
    }
  }, [displayState.currentSegment?.text]);

  return (
    <div ref={containerRef} className="subtitle-display-new">
      {/* 背景装饰 */}
      <div className="subtitle-background">
        <div className="bg-gradient"></div>
        <div className="bg-particles"></div>
      </div>

      {/* 主显示区域 - 垂直居中 */}
      <div className="subtitle-main-area">
        
        {/* 已确认的段落 - 在上方显示 */}
        <div ref={confirmedSegmentsRef} className="confirmed-segments-area">
          {animatedSegments.map((segment, index) => (
            <div 
              key={segment.id} 
              className={`animated-segment ${index === animatedSegments.length - 1 ? 'latest' : 'previous'}`}
            >
              <div className="segment-text">{segment.text}</div>
            </div>
          ))}
        </div>

        {/* 当前正在识别的段落 - 居中显示 */}
        <div ref={currentSegmentRef} className="current-segment-area">
          <div className="typing-text">
            {/* 空状态 - 在typing-text内部显示 */}
            {!displayState.currentSegment && animatedSegments.length === 0 && displayState.confirmedSegments.length === 0 && (
              <div className="empty-state">
                <div className="microphone-icon">🎤</div>
                <div className="empty-message">开始说话，实时字幕将在这里显示</div>
                <div className="empty-hint">支持中文和英文语音识别</div>
              </div>
            )}
          </div>
          {displayState.currentSegment && (
            <div className={`typing-indicator ${isRecording ? 'recording' : ''}`}>
              <span className="dot"></span>
              <span className="dot"></span>
              <span className="dot"></span>
            </div>
          )}
        </div>
      </div>

    </div>
  );
};

export default SubtitleDisplay; 