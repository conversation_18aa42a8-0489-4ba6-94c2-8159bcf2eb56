# WhisperSync

WhisperSync 是一个跨平台桌面应用，提供实时语音识别和翻译功能。应用采用模块化设计，确保在 Mac 和 Windows 平台上提供一致的用户体验，同时保持高效的性能和资源利用率。

**🆕 v2.0 更新**: 现已完全迁移到 [WhisperLiveKit](https://github.com/QuentinFuxa/WhisperLiveKit)，提供更专业的实时语音识别体验！

## 功能特点

- **实时语音识别**: 基于 WhisperLiveKit 的高效实时语音识别
- **本地处理**: 完全本地化处理，保护隐私安全
- **跨平台支持**: 在 Mac 和 Windows 上提供一致的用户体验
- **模块化设计**: 清晰的架构，便于扩展和维护
- **说话人分离**: 支持多说话人识别（可选）
- **置信度显示**: 实时显示识别置信度
- **自动静音检测**: 智能语音活动检测

## 技术栈

- **前端**: React + TypeScript
- **桌面框架**: Electron
- **语音识别**: WhisperLiveKit (基于 OpenAI Whisper)
- **后端服务**: FastAPI + Python
- **通信协议**: WebSocket

## 开发环境设置

### 前提条件

- **Node.js**: v16.0.0 或更高版本
- **Python**: v3.8 或更高版本
- **操作系统**: Windows, macOS 或 Linux

### 快速开始

#### 方法 1: 完整设置（推荐）

这种方法会清理项目，安装所有依赖，并创建 Python 虚拟环境：

```bash
# 清理项目并完整设置
npm run reset
```

#### 方法 2: 分步设置

如果您想更精细地控制设置过程，可以按照以下步骤操作：

```bash
# 1. 清理项目（可选）
npm run clean

# 2. 安装 Node.js 依赖
npm install

# 3. 创建 Python 虚拟环境并安装 WhisperLiveKit
npm run setup:whisperlivekit
```

### 启动应用

设置完成后，您可以启动应用：

```bash
npm run electron:dev
```

## 详细说明

### 项目结构

- `electron/`: Electron 主进程代码
- `src/`: 前端 React 代码
- `scripts/`: 项目脚本，包括设置和启动脚本
- `venv/`: Python 虚拟环境（由 setup:whisperlivekit 脚本创建）
- `whisperlivekit_server.py`: 动态生成的 WhisperLiveKit 服务器脚本

### WhisperLiveKit 集成

项目使用 WhisperLiveKit 作为语音识别后端，提供以下优势：

- **专业的实时语音识别**: 专门为实时场景优化
- **多用户支持**: 同时处理多个客户端连接
- **自动缓冲管理**: 智能音频缓冲和分块
- **置信度验证**: 实时验证识别结果的可信度
- **说话人分离**: 可选的多说话人识别功能

### 虚拟环境

项目使用 Python 虚拟环境来隔离依赖，避免与系统 Python 环境冲突。虚拟环境位于项目根目录的 `venv` 文件夹中。

#### 手动激活虚拟环境

如果您需要在虚拟环境中运行其他 Python 命令，可以手动激活它：

**Windows**:
```bash
.\venv\Scripts\activate
```

**macOS/Linux**:
```bash
source venv/bin/activate
```

或者使用项目提供的快捷脚本：

**Windows**:
```bash
.\activate.bat
```

**macOS/Linux**:
```bash
source activate.sh
```

### 可用的 npm 脚本

- `npm run dev`: 启动 Vite 开发服务器（仅前端）
- `npm run electron:dev`: 启动完整的 Electron 应用（包括前端和 WhisperLiveKit 后端）
- `npm run setup:whisperlivekit`: 创建 Python 虚拟环境并安装 WhisperLiveKit
- `npm run clean`: 清理项目，删除生成的文件和依赖
- `npm run setup`: 安装 Node.js 依赖并设置 WhisperLiveKit 环境
- `npm run reset`: 清理项目并完整设置
- `npm run start:whisperlivekit-server`: 单独启动 WhisperLiveKit 服务器

### 故障排除

#### macOS ARM64 (M1/M2/M3) 平台特别说明

对于 Apple Silicon (M1/M2/M3) Mac 用户，WhisperLiveKit 会自动使用 MPS (Metal Performance Shaders) 进行加速。如果遇到问题，请尝试：

1. 确保已安装 Xcode Command Line Tools：
   ```bash
   xcode-select --install
   ```

2. 手动安装 WhisperLiveKit：
   ```bash
   # 激活虚拟环境
   source venv/bin/activate
   
   # 安装 WhisperLiveKit
   pip install whisperlivekit
   ```

#### Python 依赖安装失败

如果 Python 依赖安装失败，可能是因为某些包需要编译器或其他系统依赖。请尝试：

1. 确保已安装 Python 开发工具包
2. 对于 PyTorch，可以访问 [PyTorch 官网](https://pytorch.org/get-started/locally/) 获取适合您系统的安装命令
3. 手动安装依赖：
   ```bash
   # 激活虚拟环境
   source venv/bin/activate  # 或 .\venv\Scripts\activate (Windows)
   
   # 安装核心依赖
   pip install whisperlivekit fastapi uvicorn websockets
   ```

#### WhisperLiveKit 服务器启动失败

如果 WhisperLiveKit 服务器启动失败，请检查：

1. Python 虚拟环境是否正确创建
2. WhisperLiveKit 是否正确安装
3. 端口 8765 是否被其他程序占用
4. 查看控制台输出获取详细错误信息

#### 其他问题

如果遇到其他问题，请尝试：

1. 完全重置项目：`npm run reset`
2. 检查控制台输出，查找错误信息
3. 确保系统满足所有要求
4. 查看 WhisperLiveKit 的官方文档

## 高级用法

### 自定义 WhisperLiveKit 配置

您可以修改 `scripts/start-whisperlivekit-server.js` 文件中的服务器脚本来自定义 WhisperLiveKit 的配置：

- 更改模型大小（tiny.en, base.en, small.en, medium.en, large）
- 启用说话人分离功能
- 调整语言设置
- 配置置信度阈值

### 使用不同的模型

WhisperLiveKit 支持多种模型大小，您可以根据需要选择：

- `tiny.en`: 最快，准确度较低
- `base.en`: 平衡速度和准确度
- `small.en`: 更好的准确度
- `medium.en`: 高准确度
- `large`: 最高准确度，但速度较慢

### 启用说话人分离

要启用说话人分离功能，需要安装额外的依赖：

```bash
# 激活虚拟环境
source venv/bin/activate

# 安装说话人分离依赖
pip install pyannote.audio
```

然后在服务器配置中启用 `diarization=True`。

## 从 v1.x 迁移

如果您从旧版本的 WhisperSync 迁移，请注意以下变化：

1. **后端引擎**: 从 whisper.cpp 迁移到 WhisperLiveKit
2. **依赖管理**: 现在使用 Python 虚拟环境管理依赖
3. **配置方式**: 配置现在通过 Python 脚本而不是命令行参数
4. **新功能**: 支持说话人分离和置信度显示

## 贡献指南

1. Fork 仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

[MIT](LICENSE)

## 致谢

- [WhisperLiveKit](https://github.com/QuentinFuxa/WhisperLiveKit) - 用于实时语音识别
- [OpenAI Whisper](https://github.com/openai/whisper) - 语音识别模型
- [FastAPI](https://fastapi.tiangolo.com/) - 用于构建 API 服务器
- [Electron](https://www.electronjs.org/) - 用于构建跨平台桌面应用
- [React](https://reactjs.org/) - 用于构建用户界面
