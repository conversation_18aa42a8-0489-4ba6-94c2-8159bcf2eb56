set(WHISPER_VERSION      @WHISPER_INSTALL_VERSION@)
set(WHISPER_BUILD_COMMIT @WHISPER_BUILD_COMMIT@)
set(WHISPER_BUILD_NUMBER @WHISPER_BUILD_NUMBER@)
set(WHISPER_SHARED_LIB   @BUILD_SHARED_LIBS@)

set(GGML_BLAS       @GGML_BLAS@)
set(GGML_CUDA       @GGML_CUDA@)
set(GGML_METAL      @GGML_METAL@)
set(G<PERSON>L_HIPBLAS    @GGML_HIPBLAS@)
set(G<PERSON><PERSON>_ACCELERATE @GGML_ACCELERATE@)

@PACKAGE_INIT@

set_and_check(WHISPER_INCLUDE_DIR "@PACKAGE_WHISPER_INCLUDE_INSTALL_DIR@")
set_and_check(WHISPER_LIB_DIR     "@PACKAGE_WHISPER_LIB_INSTALL_DIR@")
set_and_check(WHIS<PERSON>ER_BIN_DIR     "@PACKAGE_WHISPER_BIN_INSTALL_DIR@")

# Ensure transient dependencies satisfied

find_package(Threads REQUIRED)

if (APPLE AND GGML_ACCELERATE)
    find_library(ACCELERATE_FRAMEWORK Accelerate REQUIRED)
endif()

if (GGML_BLAS)
    find_package(BLAS REQUIRED)
endif()

if (GGML_CUDA)
    find_package(CUDAToolkit REQUIRED)
endif()

if (GGML_METAL)
    find_library(FOUNDATION_LIBRARY Foundation REQUIRED)
    find_library(METAL_FRAMEWORK Metal REQUIRED)
    find_library(METALKIT_FRAMEWORK MetalKit REQUIRED)
endif()

if (GGML_HIPBLAS)
    find_package(hip REQUIRED)
    find_package(hipblas REQUIRED)
    find_package(rocblas REQUIRED)
endif()

find_library(whisper_LIBRARY whisper
    REQUIRED
    HINTS ${WHISPER_LIB_DIR})

set(_whisper_link_deps "Threads::Threads" "@WHISPER_EXTRA_LIBS@")
set(_whisper_transient_defines "@WHISPER_TRANSIENT_DEFINES@")

add_library(whisper UNKNOWN IMPORTED)

set_target_properties(whisper
    PROPERTIES
    INTERFACE_INCLUDE_DIRECTORIES "${WHISPER_INCLUDE_DIR}"
        INTERFACE_LINK_LIBRARIES "${_whisper_link_deps}"
        INTERFACE_COMPILE_DEFINITIONS "${_whisper_transient_defines}"
        IMPORTED_LINK_INTERFACE_LANGUAGES "CXX"
        IMPORTED_LOCATION "${whisper_LIBRARY}"
        INTERFACE_COMPILE_FEATURES cxx_std_11
        POSITION_INDEPENDENT_CODE ON )

check_required_components(whisper)
