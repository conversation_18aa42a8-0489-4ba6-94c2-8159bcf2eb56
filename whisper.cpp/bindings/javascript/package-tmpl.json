{"name": "whisper.cpp", "version": "@PROJECT_VERSION@", "description": "Whisper speech recognition", "main": "whisper.js", "scripts": {"test": "echo \"todo: add tests\" && exit 0"}, "repository": {"type": "git", "url": "git+https://github.com/ggerganov/whisper.cpp"}, "keywords": ["openai", "whisper", "speech-to-text", "speech-recognition", "transformer"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/ggerganov/whisper.cpp/issues"}, "homepage": "https://github.com/ggerganov/whisper.cpp#readme"}