package io.github.ggerganov.whispercpp;

import io.github.ggerganov.whispercpp.ggml.GgmlTensor;
import io.github.ggerganov.whispercpp.model.EModel;

public class WhisperModel {
//    EModel type = EModel.MODEL_UNKNOWN;
//
//    WhisperHParams hparams;
//    WhisperFilters filters;
//
//    // encoder.positional_embedding
//    GgmlTensor e_pe;
//
//    // encoder.conv1
//    GgmlTensor e_conv_1_w;
//    GgmlTensor e_conv_1_b;
//
//    // encoder.conv2
//    GgmlTensor e_conv_2_w;
//    GgmlTensor e_conv_2_b;
//
//    // encoder.ln_post
//    GgmlTensor e_ln_w;
//    GgmlTensor e_ln_b;
//
//    // decoder.positional_embedding
//    GgmlTensor d_pe;
//
//    // decoder.token_embedding
//    GgmlTensor d_te;
//
//    // decoder.ln
//    GgmlTensor d_ln_w;
//    GgmlTensor d_ln_b;
//
//    std::vector<whisper_layer_encoder> layers_encoder;
//    std::vector<whisper_layer_decoder> layers_decoder;
//
//    // context
//    struct ggml_context * ctx;
//
//    // the model memory buffer is read-only and can be shared between processors
//    std::vector<uint8_t> * buf;
//
//    // tensors
//    int n_loaded;
//    Map<String, GgmlTensor> tensors;
}
