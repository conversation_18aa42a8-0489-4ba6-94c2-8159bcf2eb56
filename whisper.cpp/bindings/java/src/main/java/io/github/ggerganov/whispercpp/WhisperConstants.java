package io.github.ggerganov.whispercpp;

/**
 * Presets for alignment heads in DTW token timestamps
 */
public class WhisperConstants {
    // Alignment heads presets
    public static final int WHISPER_AHEADS_NONE = 0;
    public static final int WHISPER_AHEADS_TINY_EN = 1;
    public static final int WHISPER_AHEADS_TINY = 2;
    public static final int WHISPER_AHEADS_BASE_EN = 3;
    public static final int WHISPER_AHEADS_BASE = 4;
    public static final int WHISPER_AHEADS_SMALL_EN = 5;
    public static final int WHISPER_AHEADS_SMALL = 6;
    public static final int WHISPER_AHEADS_MEDIUM_EN = 7;
    public static final int WHISPER_AHEADS_MEDIUM = 8;
    public static final int WHISPER_AHEADS_LARGE_V1 = 9;
    public static final int WHISPER_AHEADS_LARGE_V2 = 10;
    public static final int WHISPER_AHEADS_LARGE_V3 = 11;
    public static final int WHISPER_AHEADS_LARGE_V3_TURBO = 12;
    public static final int WHISPER_AHEADS_CUSTOM = 13;
    public static final int WHISPER_AHEADS_N_TOP_MOST = 14;
    public static final int WHISPER_AHEADS_COUNT = 15;
}
