import { spawn, execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

// 配置
const venvPath = path.join(projectRoot, 'venv');
const requirementsPath = path.join(projectRoot, 'requirements.txt');

// 检测操作系统
const isWindows = process.platform === 'win32';
const isMacOS = process.platform === 'darwin';
const isLinux = process.platform === 'linux';

// 获取虚拟环境中的 Python 和 pip 路径
const venvPython = isWindows 
  ? path.join(venvPath, 'Scripts', 'python.exe')
  : path.join(venvPath, 'bin', 'python');

const venvPip = isWindows 
  ? path.join(venvPath, 'Scripts', 'pip.exe')
  : path.join(venvPath, 'bin', 'pip');

console.log('🚀 开始设置 WhisperLiveKit 环境...');

// 创建 requirements.txt 文件
const requirements = `# WhisperLiveKit 和相关依赖
whisperlivekit>=0.1.7
fastapi>=0.104.0
uvicorn>=0.24.0
websockets>=12.0
torch>=2.0.0
transformers>=4.35.0
numpy>=1.24.0
scipy>=1.11.0
librosa>=0.10.0
soundfile>=0.12.0

# Apple Silicon 优化 (MacOS ARM64)
mlx-whisper>=0.9.0; sys_platform == "darwin" and platform_machine == "arm64"

# 可选：说话人分离功能
# pyannote.audio>=3.1.0

# 开发依赖
requests>=2.31.0
`;

// 创建 requirements.txt
fs.writeFileSync(requirementsPath, requirements);
console.log('📝 已创建 requirements.txt');

async function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🔧 执行: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: isWindows,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`命令执行失败，退出码: ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function checkPython() {
  try {
    const version = execSync('python --version', { encoding: 'utf8' });
    console.log(`✅ 找到 Python: ${version.trim()}`);
    return 'python';
  } catch {
    try {
      const version = execSync('python3 --version', { encoding: 'utf8' });
      console.log(`✅ 找到 Python3: ${version.trim()}`);
      return 'python3';
    } catch {
      throw new Error('❌ 未找到 Python，请先安装 Python 3.8+');
    }
  }
}

async function createVirtualEnv() {
  if (fs.existsSync(venvPath)) {
    console.log('📁 虚拟环境已存在，跳过创建');
    return;
  }

  console.log('🏗️ 创建 Python 虚拟环境...');
  const pythonCmd = await checkPython();
  
  try {
    await runCommand(pythonCmd, ['-m', 'venv', venvPath]);
    console.log('✅ 虚拟环境创建成功');
  } catch (error) {
    console.error('❌ 创建虚拟环境失败:', error.message);
    throw error;
  }
}

async function upgradePip() {
  console.log('⬆️ 升级 pip...');
  try {
    await runCommand(venvPython, ['-m', 'pip', 'install', '--upgrade', 'pip']);
    console.log('✅ pip 升级成功');
  } catch (error) {
    console.warn('⚠️ pip 升级失败，继续安装:', error.message);
  }
}

async function installDependencies() {
  console.log('📦 安装 WhisperLiveKit 依赖...');
  
  try {
    // 首先安装 PyTorch (根据平台选择合适的版本)
    console.log('🔥 安装 PyTorch...');
    if (isMacOS) {
      // macOS 使用 CPU 版本或 MPS 版本
      await runCommand(venvPip, ['install', 'torch', 'torchvision', 'torchaudio']);
      
      // Apple Silicon 特别安装 MLX Whisper
      if (process.arch === 'arm64') {
        console.log('🍎 检测到 Apple Silicon，安装 MLX Whisper...');
        try {
          await runCommand(venvPip, ['install', 'mlx-whisper']);
          console.log('✅ MLX Whisper 安装成功，将获得显著性能提升');
        } catch (mlxError) {
          console.warn('⚠️ MLX Whisper 安装失败，将使用 faster-whisper 作为备选');
        }
      }
    } else if (isWindows || isLinux) {
      // Windows/Linux 可以使用 CUDA 版本
      await runCommand(venvPip, ['install', 'torch', 'torchvision', 'torchaudio', '--index-url', 'https://download.pytorch.org/whl/cpu']);
    }
    
    // 安装其他依赖
    console.log('📚 安装其他依赖...');
    await runCommand(venvPip, ['install', '-r', requirementsPath]);
    
    console.log('✅ 所有依赖安装成功');
  } catch (error) {
    console.error('❌ 依赖安装失败:', error.message);
    
    // 尝试逐个安装核心依赖
    console.log('🔄 尝试逐个安装核心依赖...');
    const coreDeps = [
      'whisperlivekit',
      'fastapi',
      'uvicorn',
      'websockets'
    ];
    
    for (const dep of coreDeps) {
      try {
        console.log(`📦 安装 ${dep}...`);
        await runCommand(venvPip, ['install', dep]);
        console.log(`✅ ${dep} 安装成功`);
      } catch (depError) {
        console.error(`❌ ${dep} 安装失败:`, depError.message);
      }
    }
  }
}

async function verifyInstallation() {
  console.log('🔍 验证安装...');
  
  try {
    // 检查 WhisperLiveKit 是否正确安装
    await runCommand(venvPython, ['-c', 'import whisperlivekit; print("WhisperLiveKit 版本:", whisperlivekit.__version__)']);
    console.log('✅ WhisperLiveKit 验证成功');
    
    // 检查其他核心依赖
    const imports = [
      'import fastapi',
      'import uvicorn', 
      'import websockets',
      'import torch'
    ];
    
    for (const importCmd of imports) {
      await runCommand(venvPython, ['-c', importCmd]);
    }
    
    console.log('✅ 所有核心依赖验证成功');
  } catch (error) {
    console.warn('⚠️ 部分依赖验证失败:', error.message);
    console.log('💡 这可能不会影响基本功能，可以继续使用');
  }
}

async function createActivationScript() {
  // 创建激活脚本
  const activateScript = isWindows 
    ? `@echo off
echo 激活 WhisperSync Python 虚拟环境...
call "${path.join(venvPath, 'Scripts', 'activate.bat')}"
echo 虚拟环境已激活！
echo 使用 'deactivate' 命令退出虚拟环境
cmd /k`
    : `#!/bin/bash
echo "激活 WhisperSync Python 虚拟环境..."
source "${path.join(venvPath, 'bin', 'activate')}"
echo "虚拟环境已激活！"
echo "使用 'deactivate' 命令退出虚拟环境"
exec "$SHELL"`;

  const scriptPath = isWindows 
    ? path.join(projectRoot, 'activate.bat')
    : path.join(projectRoot, 'activate.sh');

  fs.writeFileSync(scriptPath, activateScript);
  
  if (!isWindows) {
    // 给 shell 脚本添加执行权限
    try {
      execSync(`chmod +x "${scriptPath}"`);
    } catch (error) {
      console.warn('⚠️ 无法设置脚本执行权限:', error.message);
    }
  }
  
  console.log(`✅ 激活脚本已创建: ${scriptPath}`);
}

async function main() {
  try {
    await createVirtualEnv();
    await upgradePip();
    await installDependencies();
    await verifyInstallation();
    await createActivationScript();
    
    console.log('\n🎉 WhisperLiveKit 环境设置完成！');
    console.log('\n📋 下一步:');
    console.log('1. 运行 npm run electron:dev 启动应用');
    console.log('2. 或者手动激活虚拟环境测试:');
    console.log(`   ${isWindows ? '.\\activate.bat' : 'source activate.sh'}`);
    console.log('3. 测试 WhisperLiveKit:');
    console.log('   whisperlivekit-server --model tiny.en');
    
  } catch (error) {
    console.error('\n❌ 设置失败:', error.message);
    console.log('\n🔧 故障排除建议:');
    console.log('1. 确保已安装 Python 3.8+');
    console.log('2. 确保网络连接正常');
    console.log('3. 尝试手动安装: pip install whisperlivekit');
    console.log('4. 查看详细错误信息并搜索解决方案');
    process.exit(1);
  }
}

main(); 