/**
 * 清理项目，删除生成的文件和依赖
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

console.log('🧹 开始清理项目...');

// 要删除的目录和文件
const itemsToDelete = [
  // Node.js 相关
  'node_modules',
  'package-lock.json',
  
  // 构建产物
  'dist',
  'dist-electron',
  '.vite',
  
  // Python 虚拟环境
  'venv',
  
  // 旧的 whisper.cpp 相关文件
  'whisper.cpp',
  'models/whisper', // 旧的 whisper 模型目录
  
  // 生成的文件
  'whisperlivekit_server.py', // 动态生成的服务器脚本
  'requirements.txt', // 动态生成的依赖文件
  
  // 缓存和临时文件
  '.DS_Store',
  'Thumbs.db',
  '*.log',
  
  // TypeScript 构建缓存
  'tsconfig.tsbuildinfo',
  'tsconfig.app.tsbuildinfo',
  'tsconfig.node.tsbuildinfo'
];

// 递归删除目录
function deleteDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        deleteDirectory(filePath);
      } else {
        fs.unlinkSync(filePath);
      }
    });
    
    fs.rmdirSync(dirPath);
    console.log(`✅ 已删除目录: ${dirPath}`);
  }
}

// 删除文件
function deleteFile(filePath) {
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    console.log(`✅ 已删除文件: ${filePath}`);
  }
}

// 删除匹配模式的文件
function deleteFilesByPattern(dir, pattern) {
  if (!fs.existsSync(dir)) return;
  
  const files = fs.readdirSync(dir);
  const regex = new RegExp(pattern.replace('*', '.*'));
  
  files.forEach(file => {
    if (regex.test(file)) {
      const filePath = path.join(dir, file);
      deleteFile(filePath);
    }
  });
}

// 执行清理
itemsToDelete.forEach(item => {
  const fullPath = path.join(projectRoot, item);
  
  if (item.includes('*')) {
    // 处理通配符模式
    const dir = path.dirname(fullPath);
    const pattern = path.basename(item);
    deleteFilesByPattern(dir, pattern);
  } else if (fs.existsSync(fullPath)) {
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      deleteDirectory(fullPath);
    } else {
      deleteFile(fullPath);
    }
  }
});

// 清理旧的激活脚本（如果存在）
const oldActivateScripts = [
  path.join(projectRoot, 'activate.bat'),
  path.join(projectRoot, 'activate.sh')
];

oldActivateScripts.forEach(script => {
  if (fs.existsSync(script)) {
    deleteFile(script);
  }
});

// 创建必要的目录结构
const dirsToCreate = [
  'models', // 保留模型目录但清空内容
];

dirsToCreate.forEach(dir => {
  const dirPath = path.join(projectRoot, dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`📁 已创建目录: ${dirPath}`);
  }
});

// 创建新的 .gitignore（如果不存在）
const gitignorePath = path.join(projectRoot, '.gitignore');
const gitignoreContent = `# Dependencies
node_modules/
venv/

# Build outputs
dist/
dist-electron/
*.tsbuildinfo

# Environment files
.env
.env.local
.env.*.local

# Generated files
whisperlivekit_server.py
requirements.txt

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Models (too large for git)
models/whisper/
models/*.bin
models/*.ggml

# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so

# Electron
out/
`;

if (!fs.existsSync(gitignorePath)) {
  fs.writeFileSync(gitignorePath, gitignoreContent);
  console.log('📝 已创建 .gitignore 文件');
}

console.log('\n🎉 项目清理完成！');
console.log('\n📋 清理内容:');
console.log('• 删除了 node_modules 和构建产物');
console.log('• 删除了旧的 whisper.cpp 相关文件');
console.log('• 删除了 Python 虚拟环境');
console.log('• 清理了缓存和临时文件');
console.log('\n💡 下一步: 运行 npm run setup 重新设置项目');

export { deleteDirectory, deleteFile };
