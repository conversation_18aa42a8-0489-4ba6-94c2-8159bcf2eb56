#!/usr/bin/env python3
"""
WhisperLiveKit 服务器适配器
直接使用 WhisperLiveKit 的 AudioProcessor 提供 WebSocket 接口
"""

import asyncio
import json
import logging
import sys
import os
import platform
from pathlib import Path
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# 添加 WhisperLiveKit 到 Python 路径
project_root = Path(__file__).parent.parent
whisperlivekit_path = project_root / "WhisperLiveKit"
if whisperlivekit_path.exists():
    sys.path.insert(0, str(whisperlivekit_path))

try:
    from whisperlivekit import WhisperLiveKit
    from whisperlivekit.audio_processor import AudioProcessor
    print("✅ 成功导入 WhisperLiveKit")
except ImportError as e:
    print(f"❌ 无法导入 WhisperLiveKit: {e}")
    print("请确保 WhisperLiveKit 已正确安装")
    sys.exit(1)

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,  # 启用详细调试日志
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 全局 WhisperLiveKit 实例
kit = None

app = FastAPI(title="WhisperSync v2.0 - WhisperLiveKit Adapter")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def detect_optimal_backend():
    """检测最优的 Whisper 后端"""
    system = platform.system()
    machine = platform.machine()
    
    logger.info(f"系统信息: {system} {machine}")
    
    # Apple Silicon 优化
    if system == "Darwin" and machine == "arm64":
        try:
            import mlx_whisper
            logger.info("🍎 检测到 Apple Silicon，使用 MLX Whisper 后端")
            return "mlx-whisper"
        except ImportError:
            logger.warning("⚠️ MLX Whisper 未安装，降级到 faster-whisper")
            return "faster-whisper"
    
    # NVIDIA GPU 检测
    try:
        import torch
        if torch.cuda.is_available():
            logger.info("🚀 检测到 CUDA GPU，使用 faster-whisper")
            return "faster-whisper"
    except ImportError:
        pass
    
    # 默认后端
    logger.info("🔧 使用默认 faster-whisper 后端")
    return "faster-whisper"

def select_optimal_model():
    """选择最优的 Whisper 模型"""
    system_info = platform.platform().lower()

    if "darwin" in system_info and platform.machine() == "arm64":
        # Apple Silicon: 使用 base 模型平衡性能和质量
        return "base"
    else:
        # 其他系统也使用 base 模型，平衡性能和质量
        return "base"

@app.on_event("startup")
async def startup_event():
    """初始化 WhisperLiveKit"""
    global kit
    try:
        logger.info("🚀 初始化 WhisperLiveKit...")
        
        optimal_backend = detect_optimal_backend()
        optimal_model = select_optimal_model()
        
        logger.info(f"选择的后端: {optimal_backend}")
        logger.info(f"选择的模型: {optimal_model}")
        
        # 针对 MLX Whisper 的优化配置（暂时禁用说话人分离）
        if optimal_backend == "mlx-whisper":
            logger.info("🍎 使用 MLX Whisper 优化配置（专注语音识别性能）")
            kit = WhisperLiveKit(
                model=optimal_model,
                language="auto",
                backend=optimal_backend,
                diarization=False,  # 暂时禁用说话人分离，专注解决核心问题
                vac=False,  # 禁用VAC以减少处理开销
                min_chunk_size=1.0,  # 增加块大小，减少频繁处理
                confidence_validation=False,  # 禁用置信度验证以减少延迟
                buffer_trimming_sec=2.0,  # 大幅减少缓冲区，提高实时性
                buffer_trimming="segment"  # 使用段落分割而非句子分割
            )
        else:
            logger.info("🔧 使用通用优化配置（专注语音识别性能）")
            kit = WhisperLiveKit(
                model=optimal_model,
                language="auto",
                backend=optimal_backend,
                diarization=False,  # 暂时禁用说话人分离，专注解决核心问题
                vac=True,  # 其他后端保留VAC
                min_chunk_size=0.5,  # 适中的块大小
                confidence_validation=False,  # 禁用置信度验证以减少延迟
                buffer_trimming_sec=3.0,  # 减少缓冲区大小
                buffer_trimming="segment"  # 使用段落分割而非句子分割
            )
        logger.info("✅ WhisperLiveKit 初始化成功")
    except Exception as e:
        logger.error(f"❌ WhisperLiveKit 初始化失败: {e}")
        raise

@app.get("/")
async def get_root():
    """提供简单的状态页面"""
    return HTMLResponse("""
    <!DOCTYPE html>
    <html>
    <head>
        <title>WhisperSync v2.0 - WhisperLiveKit Adapter</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .status { color: green; font-weight: bold; }
            .info { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        </style>
    </head>
    <body>
        <h1>WhisperSync v2.0</h1>
        <p class="status">✅ WhisperLiveKit 适配器运行中</p>
        <div class="info">
            <h3>连接信息:</h3>
            <p><strong>WebSocket 端点:</strong> ws://localhost:8889/ws</p>
            <p><strong>状态:</strong> 就绪</p>
            <p><strong>后端:</strong> WhisperLiveKit (优化配置)</p>
        </div>
    </body>
    </html>
    """)

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "WhisperSync v2.0 - WhisperLiveKit Adapter",
        "whisperlivekit_ready": kit is not None
    }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """主要的 WebSocket 端点"""
    await websocket.accept()
    logger.info("🔌 WebSocket 连接已建立")
    
    # 发送连接成功消息
    await websocket.send_json({
        "type": "CONNECTION_SUCCESS",
        "message": "WhisperSync v2.0 - WhisperLiveKit Adapter (直接模式)",
        "timestamp": asyncio.get_event_loop().time()
    })
    
    audio_processor = None
    websocket_task = None
    
    try:
        # 直接使用原始的 AudioProcessor
        audio_processor = AudioProcessor()
        logger.info("📱 原始音频处理器已创建")
        
        # 启动结果处理任务
        results_generator = await audio_processor.create_tasks()
        logger.info("🔄 结果生成器已创建")
        
        # WhisperLiveKit格式转换器
        def convert_whisperlivekit_to_frontend(whisperlivekit_result):
            """将WhisperLiveKit格式转换为前端期望的格式"""
            if not isinstance(whisperlivekit_result, dict):
                return None

            status = whisperlivekit_result.get("status", "unknown")
            lines = whisperlivekit_result.get("lines", [])
            buffer_transcription = whisperlivekit_result.get("buffer_transcription", "")
            buffer_diarization = whisperlivekit_result.get("buffer_diarization", "")

            # 处理确认的文本行
            confirmed_segments = []
            for line in lines:
                if line.get("text", "").strip():
                    confirmed_segments.append({
                        "type": "transcription",
                        "text": line["text"].strip(),
                        "is_final": True,
                        "speaker": line.get("speaker", 1),
                        "start_time": line.get("beg", "00:00:00"),
                        "end_time": line.get("end", "00:00:00"),
                        "confidence": 0.9  # WhisperLiveKit不提供置信度，使用默认值
                    })

            # 处理实时缓冲区文本
            current_segment = None
            if buffer_transcription.strip():
                current_segment = {
                    "type": "transcription",
                    "text": buffer_transcription.strip(),
                    "is_final": False,
                    "speaker": 1,  # 缓冲区文本默认说话人1
                    "confidence": 0.7
                }
            elif buffer_diarization.strip():
                current_segment = {
                    "type": "transcription",
                    "text": buffer_diarization.strip(),
                    "is_final": False,
                    "speaker": 1,
                    "confidence": 0.7
                }

            return {
                "type": "WHISPERLIVEKIT_RESULT",
                "status": status,
                "confirmed_segments": confirmed_segments,
                "current_segment": current_segment,
                "remaining_time_transcription": whisperlivekit_result.get("remaining_time_transcription", 0),
                "remaining_time_diarization": whisperlivekit_result.get("remaining_time_diarization", 0),
                "raw_whisperlivekit": whisperlivekit_result  # 保留原始数据用于调试
            }

        # 创建结果处理任务，添加详细调试和格式转换
        async def debug_handle_results():
            result_count = 0
            try:
                logger.info("🎯 开始监听转录结果...")
                async for result in results_generator:
                    result_count += 1
                    logger.info(f"📋 [结果 #{result_count}] 收到转录数据: {type(result)}")
                    logger.debug(f"📋 [结果 #{result_count}] 完整内容: {result}")

                    if result:
                        # 转换WhisperLiveKit格式为前端格式
                        converted_result = convert_whisperlivekit_to_frontend(result)
                        if converted_result:
                            await websocket.send_json(converted_result)
                            logger.info(f"📤 [结果 #{result_count}] 已转换并发送给前端")
                            logger.debug(f"📤 [结果 #{result_count}] 转换后格式: {converted_result}")
                        else:
                            logger.warning(f"⚠️ [结果 #{result_count}] 格式转换失败")
                    else:
                        logger.debug(f"📋 [结果 #{result_count}] 空结果，跳过")

                logger.info("🏁 结果生成器已完成")
                await websocket.send_json({"type": "ready_to_stop"})

            except Exception as e:
                logger.error(f"❌ 结果处理异常: {e}")
                logger.exception("详细错误信息:")
        
        websocket_task = asyncio.create_task(debug_handle_results())
        
        # 主消息循环
        while True:
            try:
                # 接收 WebSocket 消息（可能是音频数据或 JSON 控制消息）
                raw_message = await websocket.receive()
                logger.debug(f"收到 WebSocket 消息类型: {raw_message.get('type')}")
                
                # 处理二进制音频数据
                if raw_message.get("type") == "websocket.receive" and "bytes" in raw_message:
                    audio_data = raw_message["bytes"]
                    logger.debug(f"📥 收到音频数据: {len(audio_data)} 字节")
                    
                    # 检查是否为空数据（停止信号）
                    if len(audio_data) == 0:
                        logger.info("🛑 收到空音频数据（停止信号）")
                        # 不发送空数据给处理器，避免问题
                        await websocket.send_json({
                            "type": "RECORDING_STOPPED",
                            "data": {"timestamp": asyncio.get_event_loop().time()}
                        })
                        continue
                    
                    # 处理音频数据
                    await audio_processor.process_audio(audio_data)
                    logger.debug(f"📤 音频数据已发送给处理器")
                
                # 处理 JSON 控制消息
                elif raw_message.get("type") == "websocket.receive" and "text" in raw_message:
                    try:
                        message = json.loads(raw_message["text"])
                        logger.debug(f"📥 收到 JSON 控制消息: {message}")
                        
                        # 处理控制消息
                        if message.get("type") == "CLIENT_CONNECTED":
                            logger.info(f"🔗 客户端连接确认: {message.get('userAgent', 'Unknown')[:50]}")
                            await websocket.send_json({
                                "type": "CONNECTION_SUCCESS",
                                "message": "WhisperSync v2.0 - WhisperLiveKit Adapter (直接模式)",
                                "timestamp": asyncio.get_event_loop().time()
                            })
                        elif message.get("type") == "START_RECORDING":
                            logger.info("🎤 开始录音")
                            await websocket.send_json({
                                "type": "RECORDING_STARTED",
                                "data": {"timestamp": asyncio.get_event_loop().time()}
                            })
                        elif message.get("type") == "STOP_RECORDING":
                            logger.info("⏹️ 停止录音（JSON 信号）")
                            # 发送空音频数据作为停止信号给AudioProcessor
                            if audio_processor:
                                logger.info("📤 发送停止信号给音频处理器")
                                await audio_processor.process_audio(b"")  # 空数据触发停止
                            await websocket.send_json({
                                "type": "RECORDING_STOPPED",
                                "data": {"timestamp": asyncio.get_event_loop().time()}
                            })
                        elif message.get("type") == "TEST":
                            logger.info("🧪 收到连接测试")
                            await websocket.send_json({
                                "type": "TEST_RESPONSE",
                                "message": "连接测试成功",
                                "timestamp": asyncio.get_event_loop().time()
                            })
                        else:
                            logger.warning(f"未知的消息类型: {message}")
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON 解析失败: {e}, 原始文本: {raw_message.get('text', '')[:100]}")
                
                # 处理连接断开
                elif raw_message.get("type") == "websocket.disconnect":
                    logger.info("🔌 WebSocket 连接断开")
                    break
                
                else:
                    logger.warning(f"未知的 WebSocket 消息类型: {raw_message}")
                    
            except WebSocketDisconnect:
                logger.info("🔌 WebSocket 连接断开")
                # 确保AudioProcessor收到停止信号
                if audio_processor:
                    logger.info("📤 WebSocket断开，发送停止信号给音频处理器")
                    try:
                        await audio_processor.process_audio(b"")  # 发送停止信号
                    except Exception as stop_error:
                        logger.error(f"发送停止信号时出错: {stop_error}")
                break
            except Exception as e:
                logger.error(f"WebSocket 消息处理出错: {e}")
                logger.exception("详细错误信息:")
                
                # 尝试发送错误消息给客户端
                try:
                    await websocket.send_json({
                        "type": "ERROR",
                        "message": f"音频处理错误: {str(e)}",
                        "timestamp": asyncio.get_event_loop().time()
                    })
                except:
                    logger.error("无法发送错误消息给客户端")
                continue  # 继续处理下一个消息而不是退出
                
    except Exception as e:
        logger.error(f"WebSocket 端点出错: {e}")
        logger.exception("WebSocket 端点详细错误:")
    finally:
        # 清理资源
        logger.info("🧹 清理 WebSocket 连接资源")
        if websocket_task:
            websocket_task.cancel()
            logger.info("WebSocket 结果处理任务已取消")
        
        if audio_processor:
            try:
                # 确保发送停止信号
                logger.info("📤 最终清理：发送停止信号给音频处理器")
                await audio_processor.process_audio(b"")  # 确保停止信号被发送

                # 调用 AudioProcessor 的清理方法
                if hasattr(audio_processor, 'cleanup'):
                    await audio_processor.cleanup()
                    logger.info("音频处理器已清理")
                else:
                    logger.warning("音频处理器没有 cleanup 方法")
            except Exception as cleanup_error:
                logger.error(f"清理音频处理器时出错: {cleanup_error}")
                
        logger.info("🔌 WebSocket 连接资源清理完成")

def main():
    """启动服务器"""
    logger.info("🚀 启动 WhisperSync v2.0 - WhisperLiveKit Adapter")
    
    # 检查 WhisperLiveKit 是否可用
    try:
        import whisperlivekit
        logger.info(f"WhisperLiveKit 版本: {getattr(whisperlivekit, '__version__', 'unknown')}")
    except ImportError:
        logger.error("WhisperLiveKit 未安装，请运行: pip install whisperlivekit")
        sys.exit(1)
    
    # 启动服务器
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8889,
        log_level="info"
    )

if __name__ == "__main__":
    main() 