#!/usr/bin/env node

import { spawn, exec } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 使用新的 WhisperLiveKit 适配器
const PYTHON_SCRIPT = 'whisperlivekit-server-adapter.py';
const VERSION_NAME = 'WhisperLiveKit 原生适配器';

const SCRIPT_PATH = path.join(__dirname, PYTHON_SCRIPT);
const PORT = 8889;

console.log(`🚀 WhisperSync v2.0 - 启动 ${VERSION_NAME} 服务器...`);

function checkPort(port) {
    return new Promise((resolve) => {
        exec(`lsof -ti:${port}`, (error, stdout) => {
            if (stdout.trim()) {
                const pid = stdout.trim();
                console.log(`发现端口 ${port} 被进程 ${pid} 占用`);
                resolve(pid);
            } else {
                resolve(null);
            }
        });
    });
}

function killProcess(pid) {
    return new Promise((resolve) => {
        exec(`kill -9 ${pid}`, (error) => {
            if (error) {
                console.log(`无法终止进程 ${pid}: ${error.message}`);
            } else {
                console.log(`已终止进程 ${pid}`);
            }
            resolve();
        });
    });
}

async function startServer() {
    // 检查并清理现有进程
    const existingPid = await checkPort(PORT);
    if (existingPid) {
        console.log(`正在终止现有的服务器进程...`);
        await killProcess(existingPid);
        // 等待一秒让端口释放
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log(`启动 Python 脚本: ${SCRIPT_PATH}`);
    
    // 使用虚拟环境中的 Python
    const projectRoot = path.dirname(__dirname);
    const venvPython = path.join(projectRoot, 'venv', 'bin', 'python');
    
    console.log(`使用 Python 解释器: ${venvPython}`);
    
    // 启动新的服务器进程
    const serverProcess = spawn(venvPython, [SCRIPT_PATH], {
        stdio: 'inherit',
        cwd: projectRoot
    });

    serverProcess.on('error', (error) => {
        console.error(`启动服务器失败: ${error.message}`);
        process.exit(1);
    });

    serverProcess.on('exit', (code, signal) => {
        if (signal) {
            console.log(`服务器进程被信号 ${signal} 终止`);
        } else {
            console.log(`服务器进程退出，代码: ${code}`);
        }
        process.exit(code || 0);
    });

    // 处理 Ctrl+C
    process.on('SIGINT', () => {
        console.log('收到中断信号，正在关闭服务器...');
        serverProcess.kill('SIGTERM');
    });

    process.on('SIGTERM', () => {
        console.log('收到终止信号，正在关闭服务器...');
        serverProcess.kill('SIGTERM');
    });
}

startServer().catch(console.error); 