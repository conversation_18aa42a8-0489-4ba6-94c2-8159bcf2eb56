# WhisperLiveKit 集成技术文档

## 项目概述

WhisperLiveKit 是一个基于 Whisper Streaming 的实时语音转文字框架，提供完整的后端解决方案，支持实时语音识别和说话人分离功能。

## 核心架构

### 三层架构设计

1. **前端层 (Frontend)**
   - HTML + JavaScript 界面
   - 通过 WebSocket 与后端通信
   - 负责音频采集和结果展示

2. **Web 服务层 (Backend Web Server)**
   - FastAPI + WebSocket 服务器
   - 处理 WebSocket 连接和路由
   - 音频数据流管理

3. **核心处理层 (Core Backend Library)**
   - 音频处理、ASR 和说话人分离
   - 服务器无关的可重用组件
   - 支持多种 Whisper 后端

## 核心组件分析

### 1. WhisperLiveKit 核心类 (`core.py`)

```python
class WhisperLiveKit:
    def __init__(self, **kwargs):
        # 单例模式，全局配置管理
        self.args = Namespace(**merged_args)
        self.asr = None          # ASR 引擎
        self.tokenizer = None    # 分词器
        self.diarization = None  # 说话人分离
```

**主要功能：**
- 全局配置管理（模型、语言、后端等）
- ASR 引擎初始化
- 说话人分离模块初始化
- 提供内置 Web 界面

### 2. AudioProcessor 音频处理器 (`audio_processor.py`)

这是 WhisperLiveKit 的核心处理引擎：

```python
class AudioProcessor:
    def __init__(self):
        # 音频设置
        self.sample_rate = 16000
        self.channels = 1
        
        # 状态管理
        self.tokens = []                    # ASR 令牌
        self.buffer_transcription = ""      # 转录缓冲区
        self.buffer_diarization = ""        # 分离缓冲区
        self.full_transcription = ""        # 完整转录
        
        # 处理组件
        self.ffmpeg_process = None          # FFmpeg 解码器
        self.online = None                  # 在线 ASR 引擎
```

**核心方法：**

#### `async def process_audio(self, message)`
- **输入**: WebM 格式音频数据 (bytes)
- **处理**: 通过 FFmpeg 转换为 PCM 格式
- **输出**: 无返回值，数据进入处理管道

#### `async def create_tasks()`
- **功能**: 创建异步处理任务
- **返回**: `results_formatter()` 生成器
- **任务**: 转录任务、分离任务、FFmpeg 读取任务、监控任务

#### `async def results_formatter()`
- **功能**: 格式化处理结果
- **输出**: 异步生成器，产出标准化响应

### 3. FastAPI 服务器 (`basic_server.py`)

```python
@app.websocket("/asr")
async def websocket_endpoint(websocket: WebSocket):
    audio_processor = AudioProcessor()
    await websocket.accept()
    
    # 创建结果处理任务
    results_generator = await audio_processor.create_tasks()
    websocket_task = asyncio.create_task(
        handle_websocket_results(websocket, results_generator)
    )
    
    # 音频数据接收循环
    while True:
        message = await websocket.receive_bytes()
        await audio_processor.process_audio(message)
```

## WebSocket 通信协议

### 客户端 → 服务器

1. **音频数据流**
   ```javascript
   // WebM 格式音频块
   websocket.send(audioBlob);
   ```

2. **停止信号**
   ```javascript
   // 空的 Blob 表示停止录音
   const emptyBlob = new Blob([], { type: 'audio/webm' });
   websocket.send(emptyBlob);
   ```

### 服务器 → 客户端

#### 1. 实时转录响应
```json
{
  "status": "active_transcription",
  "lines": [
    {
      "speaker": 1,
      "text": "Hello world",
      "beg": "00:00:01",
      "end": "00:00:03",
      "diff": 0.5
    }
  ],
  "buffer_transcription": "current processing...",
  "buffer_diarization": "speaker analysis...",
  "remaining_time_transcription": 0.2,
  "remaining_time_diarization": 1.5
}
```

#### 2. 无音频检测
```json
{
  "status": "no_audio_detected",
  "lines": [],
  "buffer_transcription": "",
  "buffer_diarization": "",
  "remaining_time_transcription": 0,
  "remaining_time_diarization": 0
}
```

#### 3. 处理完成信号
```json
{
  "type": "ready_to_stop"
}
```

## 数据流处理管道

### 音频处理流程

1. **音频输入**: WebM → FFmpeg → PCM (16kHz, 单声道)
2. **缓冲管理**: PCM 数据累积到最小块大小
3. **并行处理**:
   - **转录管道**: PCM → Whisper → ASR 令牌
   - **分离管道**: PCM → Diart → 说话人标签
4. **结果合并**: 令牌 + 说话人标签 → 格式化输出
5. **WebSocket 推送**: JSON 响应 → 前端

### 状态管理

```python
# 线程安全的状态更新
async def update_transcription(self, new_tokens, buffer, end_buffer, full_transcription, sep):
    async with self.lock:
        self.tokens.extend(new_tokens)
        self.buffer_transcription = buffer
        # ...

async def get_current_state(self):
    async with self.lock:
        return {
            "tokens": self.tokens.copy(),
            "buffer_transcription": self.buffer_transcription,
            "remaining_time_transcription": remaining_transcription,
            # ...
        }
```

## 关键特性

### 1. 自动静音分块
- 检测静音期间自动分块，限制缓冲区大小
- 避免长时间音频导致的内存问题

### 2. 置信度验证
- 高置信度令牌立即验证，加速推理
- 可配置的置信度阈值

### 3. 多用户支持
- 每个 WebSocket 连接独立的 AudioProcessor
- 解耦的后端和在线 ASR 架构

### 4. 缓冲预览
- 显示未验证的转录片段
- 实时反馈用户当前处理状态

## 配置参数

### 核心参数
- `--model`: Whisper 模型大小 (tiny, small, medium, large)
- `--language`: 源语言代码或 'auto'
- `--backend`: Whisper 后端 (faster-whisper, mlx-whisper, openai-api)
- `--diarization`: 启用说话人分离
- `--vac`: 启用语音活动控制器

### 性能参数
- `--min-chunk-size`: 最小音频块大小（秒）
- `--buffer_trimming_sec`: 缓冲区修剪阈值（秒）
- `--confidence-validation`: 启用置信度验证

## 与现有前端的集成策略

### 1. 消息格式适配
我们需要创建适配器将 WhisperLiveKit 的响应格式转换为现有前端期望的格式：

```javascript
// 现有格式
{
  type: "transcription",
  text: "Hello world",
  is_final: true
}

// WhisperLiveKit 格式
{
  status: "active_transcription",
  lines: [{ speaker: 1, text: "Hello world", ... }],
  buffer_transcription: "...",
  // ...
}
```

### 2. 状态映射
- `active_transcription` → `transcription` 类型
- `no_audio_detected` → 特殊处理
- `ready_to_stop` → 连接关闭信号

### 3. 说话人信息处理
- 提取 `lines[].speaker` 信息
- 合并同一说话人的连续文本
- 可选择性显示说话人标签

## 部署建议

### 1. 依赖管理
```bash
pip install whisperlivekit
pip install torch  # VAC 支持
pip install diart  # 说话人分离
```

### 2. 模型下载
- 首次运行自动下载 Whisper 模型
- 说话人分离需要 HuggingFace 认证

### 3. 性能优化
- Apple Silicon: 使用 `mlx-whisper` 后端
- GPU 加速: 使用 `faster-whisper` + CUDA
- 内存限制: 调整 `buffer_trimming_sec`

## 错误处理

### 1. FFmpeg 故障恢复
- 自动检测 FFmpeg 进程状态
- 超时重启机制
- 音频流中断处理

### 2. WebSocket 连接管理
- 客户端断开检测
- 资源清理机制
- 优雅关闭流程

### 3. 模型加载失败
- 模型下载重试
- 降级到更小模型
- 错误状态反馈

这个架构为我们提供了强大的实时语音识别能力，接下来我们需要调整前端代码以适配这个新的通信协议。 