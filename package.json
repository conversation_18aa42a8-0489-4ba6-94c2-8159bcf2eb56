{"name": "whispersync", "private": true, "version": "0.0.0", "type": "module", "main": "dist-electron/electron/main.js", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "electron:simple": "electron minimal.js", "electron:dev": "concurrently --no-color --hide-group-prefix --prefix \"[{name}]\" --names \"vite,tsc,electron,whisperlivekit\" \"cross-env NODE_ENV=development vite\" \"tsc -p tsconfig.node.json -w\" \"wait-on http://localhost:5173 && electron .\" \"node scripts/start-whisperlivekit-server.js\"", "electron:build": "tsc -p tsconfig.node.json && vite build && electron-builder", "electron:preview": "npm run build && electron .", "setup:venv": "node scripts/setup-venv.js", "setup:whisperlivekit": "node scripts/setup-whisperlivekit.js", "clean": "node scripts/clean-project.js", "setup": "npm install && npm run setup:whisperlivekit", "reset": "npm run clean && npm run setup", "postinstall": "npm run setup:whisperlivekit", "start:whisperlivekit-server": "node scripts/start-whisperlivekit-server.js"}, "dependencies": {"@tailwindcss/postcss": "^4.1.7", "gsap": "^3.13.0", "react": "^19.1.0", "react-dom": "^19.1.0", "uuid": "^11.1.0", "ws": "^8.18.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.21", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^36.2.0", "electron-builder": "^26.0.12", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4.1.7", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "wait-on": "^8.0.3"}}