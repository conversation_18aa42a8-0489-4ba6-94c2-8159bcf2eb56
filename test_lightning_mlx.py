#!/usr/bin/env python3
"""
测试 Lightning MLX 是否正常工作
"""

import numpy as np
import tempfile
import os

def test_lightning_mlx():
    try:
        from lightning_whisper_mlx import LightningWhisperMLX
        print("✅ Lightning MLX 导入成功")
        
        # 初始化模型
        print("🔄 初始化 Lightning MLX 模型...")
        model = LightningWhisperMLX(model="small", batch_size=4)
        print("✅ 模型初始化成功")
        
        # 测试1: 现有的音频文件
        test_files = [
            "whisper.cpp/samples/jfk.wav",
            "whisper.cpp/samples/jfk.mp3"
        ]

        for test_file in test_files:
            if not os.path.exists(test_file):
                print(f"⚠️ 测试文件不存在: {test_file}")
                continue

            print(f"\n🎵 测试文件: {test_file}")

            try:
                # 测试转录
                print("🔄 开始转录测试...")
                result = model.transcribe(test_file)
                print(f"✅ 转录完成: {result}")

                # 分析结果
                text = result.get('text', '').strip()
                language = result.get('language', 'unknown')
                segments = result.get('segments', [])

                print(f"📝 文本: '{text}'")
                print(f"🌍 语言: {language}")
                print(f"📊 段落数: {len(segments)}")

                if text and not text.startswith('ས្'):  # 检查是否是乱码
                    print("✅ Lightning MLX 工作正常")
                else:
                    print("⚠️ Lightning MLX 返回空文本或乱码")

            except Exception as e:
                print(f"❌ 转录失败: {e}")
                import traceback
                traceback.print_exc()

        # 测试2: 模拟我们的 PCM 数据格式
        print(f"\n🧪 测试 PCM 数据格式兼容性...")
        try:
            # 读取 jfk.wav 文件的原始数据
            import soundfile as sf
            audio_data, sample_rate = sf.read("whisper.cpp/samples/jfk.wav")
            print(f"📊 原始音频: {len(audio_data)} 样本, 采样率: {sample_rate}, 类型: {audio_data.dtype}")

            # 模拟我们的处理流程：转换为 int16 再转回 float32
            # 这模拟了前端 AudioContext -> int16 -> 后端 float32 的过程
            audio_int16 = (audio_data * 32767).astype(np.int16)
            audio_float32 = audio_int16.astype(np.float32) / 32768.0

            print(f"🔄 转换后音频: {len(audio_float32)} 样本, 类型: {audio_float32.dtype}")
            print(f"📈 数据范围: {np.min(audio_float32):.4f} 到 {np.max(audio_float32):.4f}")

            # 保存为临时文件测试
            temp_file = "test_pcm_format.wav"
            sf.write(temp_file, audio_float32, 16000)
            print(f"💾 保存测试文件: {temp_file}")

            # 用 Lightning MLX 测试
            result = model.transcribe(temp_file)
            text = result.get('text', '').strip()

            print(f"📝 PCM 格式测试结果: '{text}'")

            # 清理
            if os.path.exists(temp_file):
                os.unlink(temp_file)

            if text and not text.startswith('ស្'):
                print("✅ PCM 格式兼容")
                return True
            else:
                print("❌ PCM 格式不兼容")
                return False

        except Exception as e:
            print(f"❌ PCM 格式测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
                
    except ImportError as e:
        print(f"❌ Lightning MLX 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_lightning_mlx()
