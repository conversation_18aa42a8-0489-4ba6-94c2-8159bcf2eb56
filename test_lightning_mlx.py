#!/usr/bin/env python3
"""
测试 Lightning MLX 是否正常工作
"""

import numpy as np
import tempfile
import os

def test_lightning_mlx():
    try:
        from lightning_whisper_mlx import LightningWhisperMLX
        print("✅ Lightning MLX 导入成功")
        
        # 初始化模型
        print("🔄 初始化 Lightning MLX 模型...")
        model = LightningWhisperMLX(model="small", batch_size=4)
        print("✅ 模型初始化成功")
        
        # 测试现有的音频文件
        test_files = [
            "whisper.cpp/samples/jfk.wav",
            "whisper.cpp/samples/jfk.mp3"
        ]

        for test_file in test_files:
            if not os.path.exists(test_file):
                print(f"⚠️ 测试文件不存在: {test_file}")
                continue

            print(f"\n🎵 测试文件: {test_file}")

            try:
                # 测试转录
                print("🔄 开始转录测试...")
                result = model.transcribe(test_file)
                print(f"✅ 转录完成: {result}")

                # 分析结果
                text = result.get('text', '').strip()
                language = result.get('language', 'unknown')
                segments = result.get('segments', [])

                print(f"📝 文本: '{text}'")
                print(f"🌍 语言: {language}")
                print(f"📊 段落数: {len(segments)}")

                if text and not text.startswith('ស្'):  # 检查是否是乱码
                    print("✅ Lightning MLX 工作正常")
                    return True
                else:
                    print("⚠️ Lightning MLX 返回空文本或乱码")

            except Exception as e:
                print(f"❌ 转录失败: {e}")
                import traceback
                traceback.print_exc()

        return False
                
    except ImportError as e:
        print(f"❌ Lightning MLX 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_lightning_mlx()
