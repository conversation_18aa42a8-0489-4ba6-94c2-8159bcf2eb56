#!/usr/bin/env python3
"""
测试 Lightning MLX 是否正常工作
"""

import numpy as np
import tempfile
import os

def test_lightning_mlx():
    try:
        from lightning_whisper_mlx import LightningWhisperMLX
        print("✅ Lightning MLX 导入成功")
        
        # 初始化模型
        print("🔄 初始化 Lightning MLX 模型...")
        model = LightningWhisperMLX(model="small", batch_size=4)
        print("✅ 模型初始化成功")
        
        # 创建测试音频（1秒的正弦波，440Hz）
        sample_rate = 16000
        duration = 1.0
        t = np.linspace(0, duration, int(sample_rate * duration))
        frequency = 440  # A4 音符
        audio = 0.3 * np.sin(2 * np.pi * frequency * t).astype(np.float32)
        
        print(f"🎵 创建测试音频: {len(audio)} 样本, 最大值={np.max(audio):.3f}")
        
        # 保存为临时文件
        import soundfile as sf
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as f:
            temp_file = f.name
            sf.write(temp_file, audio, sample_rate)
            print(f"💾 音频保存到: {temp_file}")
        
        try:
            # 测试转录
            print("🔄 开始转录测试...")
            result = model.transcribe(temp_file)
            print(f"✅ 转录完成: {result}")
            
            # 分析结果
            text = result.get('text', '').strip()
            language = result.get('language', 'unknown')
            segments = result.get('segments', [])
            
            print(f"📝 文本: '{text}'")
            print(f"🌍 语言: {language}")
            print(f"📊 段落数: {len(segments)}")
            
            if text:
                print("✅ Lightning MLX 工作正常")
            else:
                print("⚠️ Lightning MLX 返回空文本")
                
        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)
                print(f"🗑️ 临时文件已删除")
                
    except ImportError as e:
        print(f"❌ Lightning MLX 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_lightning_mlx()
