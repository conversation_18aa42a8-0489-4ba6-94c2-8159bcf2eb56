{
  "compilerOptions": {
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo",
    "target": "ES2022",
    "lib": ["ES2023"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    // "allowImportingTsExtensions": true, // Removed due to TS5096, not allowed when emitting JS
    "verbatimModuleSyntax": true,
    "moduleDetection": "force",
    // "noEmit": true, // We need to emit JS files for Electron main/preload
    "outDir": "./dist-electron",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true
  },
  "include": ["electron/**/*.ts", "vite.config.ts"]
}
