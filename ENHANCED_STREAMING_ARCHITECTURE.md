# 增强版流式语音识别架构设计

## 🎯 核心问题解决方案

基于您提出的关键问题，我们需要构建一个更加专业的流式语音识别系统，包含以下核心模块：

### 1. 🎤 高级 VAD (Voice Activity Detection)
### 2. 👥 实时人声分离 (Speaker Diarization) 
### 3. 📝 智能句子分割 (Sentence Segmentation)
### 4. 🔄 部分更新管理 (Partial Update Management)

## 🏗️ 增强架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   音频输入       │    │   预处理层        │    │   识别层         │
│                 │    │                  │    │                 │
│ WebRTC Audio    │───►│ Silero VAD       │───►│ Lightning MLX   │
│ 16kHz PCM       │    │ Audio Enhance    │    │ Faster-Whisper  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │                         │
                              ▼                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   输出层         │    │   后处理层        │    │   分析层         │
│                 │◄───│                  │◄───│                 │
│ Sentence Stream │    │ Sentence Seg     │    │ Pyannote Diar   │
│ Partial Updates │    │ Punctuation      │    │ Speaker Track   │
│ Speaker Labels  │    │ Confidence       │    │ Overlap Handle  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🔧 核心组件详细设计

### 1. 高级 VAD 模块

#### Silero VAD (推荐)
```python
class SileroVAD:
    """基于 Silero 的高性能 VAD"""
    
    def __init__(self):
        import torch
        self.model, _ = torch.hub.load(
            repo_or_dir='snakers4/silero-vad',
            model='silero_vad',
            force_reload=False
        )
        self.model.eval()
        
    def detect_speech(self, audio: np.ndarray, sample_rate: int = 16000) -> List[Dict]:
        """检测语音段落，返回时间戳"""
        speech_timestamps = get_speech_timestamps(
            audio, self.model, 
            sampling_rate=sample_rate,
            threshold=0.5,
            min_speech_duration_ms=250,
            min_silence_duration_ms=100
        )
        return speech_timestamps
```

#### 优势对比
| VAD 方案 | 准确率 | 延迟 | 资源占用 | 实时性 |
|----------|--------|------|----------|--------|
| 简单能量 | 60-70% | <10ms | 极低 | 优秀 |
| **Silero VAD** | **95%+** | **<50ms** | **低** | **优秀** |
| WebRTC VAD | 80-85% | <20ms | 低 | 优秀 |

### 2. 实时人声分离模块

#### Pyannote.audio (推荐)
```python
class RealTimeDiarization:
    """实时说话人分离"""
    
    def __init__(self):
        from pyannote.audio import Pipeline
        self.pipeline = Pipeline.from_pretrained(
            "pyannote/speaker-diarization-3.1",
            use_auth_token="YOUR_HF_TOKEN"  # 需要 HuggingFace 授权
        )
        
        # 实时处理配置
        self.speaker_buffer = {}
        self.current_speakers = set()
        
    def process_chunk(self, audio: np.ndarray, timestamp: float) -> Dict:
        """处理音频块，返回说话人信息"""
        # 创建临时音频文件
        temp_audio = create_temp_audio(audio, timestamp)
        
        # 执行说话人分离
        diarization = self.pipeline(temp_audio)
        
        # 提取当前时间段的说话人
        speakers = self._extract_current_speakers(diarization, timestamp)
        
        return {
            "speakers": speakers,
            "overlapping": len(speakers) > 1,
            "speaker_changes": self._detect_speaker_changes(speakers)
        }
```

#### 轻量级替代方案 (DIART)
```python
class LightweightDiarization:
    """轻量级实时说话人分离"""
    
    def __init__(self):
        from diart import OnlineSpeakerDiarization
        from diart.sources import MicrophoneAudioSource
        
        self.diarization = OnlineSpeakerDiarization()
        self.speaker_tracker = SpeakerTracker()
        
    def process_streaming(self, audio_stream):
        """流式处理说话人分离"""
        for chunk, speakers in self.diarization(audio_stream):
            yield {
                "timestamp": chunk.start,
                "duration": chunk.end - chunk.start,
                "speakers": list(speakers),
                "confidence": self._calculate_confidence(speakers)
            }
```

### 3. 智能句子分割模块

#### 基于标点符号和停顿的分割
```python
class IntelligentSegmentation:
    """智能句子分割器"""
    
    def __init__(self):
        # 句子结束标记
        self.sentence_endings = {'.', '!', '?', '。', '！', '？'}
        self.pause_threshold = 0.8  # 停顿阈值（秒）
        
        # 累积状态
        self.accumulated_text = ""
        self.confirmed_sentences = []
        self.pending_text = ""
        
    def process_transcription(self, 
                            new_text: str, 
                            confidence: float,
                            audio_timestamps: List[float]) -> Dict:
        """处理新的转录结果"""
        
        # 1. 检测部分替换
        replacement_info = self._detect_replacement(new_text)
        
        # 2. 更新累积文本
        self._update_accumulated_text(new_text, replacement_info)
        
        # 3. 句子分割
        new_sentences = self._segment_sentences()
        
        # 4. 置信度评估
        sentence_confidence = self._evaluate_confidence(new_sentences, confidence)
        
        return {
            "confirmed_sentences": new_sentences,
            "pending_text": self.pending_text,
            "replacement_info": replacement_info,
            "confidence_scores": sentence_confidence
        }
    
    def _detect_replacement(self, new_text: str) -> Dict:
        """检测部分替换情况"""
        # 使用编辑距离算法检测替换
        from difflib import SequenceMatcher
        
        matcher = SequenceMatcher(None, self.accumulated_text, new_text)
        opcodes = matcher.get_opcodes()
        
        replacements = []
        for tag, i1, i2, j1, j2 in opcodes:
            if tag == 'replace':
                replacements.append({
                    "old_text": self.accumulated_text[i1:i2],
                    "new_text": new_text[j1:j2],
                    "position": i1,
                    "confidence": matcher.ratio()
                })
        
        return {"replacements": replacements, "similarity": matcher.ratio()}
    
    def _segment_sentences(self) -> List[Dict]:
        """基于标点和停顿分割句子"""
        sentences = []
        current_sentence = ""
        
        # 基于标点符号分割
        for char in self.accumulated_text:
            current_sentence += char
            if char in self.sentence_endings:
                sentences.append({
                    "text": current_sentence.strip(),
                    "type": "punctuation_based",
                    "confidence": 0.9
                })
                current_sentence = ""
        
        # 处理未完成的句子
        if current_sentence.strip():
            self.pending_text = current_sentence.strip()
        
        return sentences
```

### 4. 部分更新管理模块

#### 智能差异检测和更新
```python
class PartialUpdateManager:
    """部分更新管理器"""
    
    def __init__(self):
        self.text_history = []
        self.confirmed_segments = []
        self.update_threshold = 0.7  # 更新置信度阈值
        
    def process_update(self, 
                      new_transcription: str,
                      timestamp: float,
                      confidence: float) -> Dict:
        """处理部分更新"""
        
        # 1. 计算与历史的差异
        diff_analysis = self._analyze_differences(new_transcription)
        
        # 2. 决定更新策略
        update_strategy = self._determine_update_strategy(
            diff_analysis, confidence
        )
        
        # 3. 执行更新
        update_result = self._execute_update(
            new_transcription, update_strategy
        )
        
        return {
            "update_type": update_strategy["type"],
            "affected_segments": update_strategy["segments"],
            "confidence": confidence,
            "changes": update_result["changes"],
            "final_text": update_result["final_text"]
        }
    
    def _determine_update_strategy(self, diff_analysis: Dict, confidence: float) -> Dict:
        """决定更新策略"""
        
        if confidence > 0.9:
            # 高置信度：直接替换
            return {"type": "replace", "segments": diff_analysis["changed_segments"]}
        elif confidence > 0.7:
            # 中等置信度：部分更新
            return {"type": "partial", "segments": diff_analysis["uncertain_segments"]}
        else:
            # 低置信度：仅追加
            return {"type": "append", "segments": []}
    
    def _execute_update(self, new_text: str, strategy: Dict) -> Dict:
        """执行更新操作"""
        changes = []
        
        if strategy["type"] == "replace":
            # 完全替换策略
            old_text = self.get_current_text()
            self.confirmed_segments = [{"text": new_text, "confidence": 0.9}]
            changes.append({
                "type": "replace_all",
                "old": old_text,
                "new": new_text
            })
            
        elif strategy["type"] == "partial":
            # 部分更新策略
            for segment_id in strategy["segments"]:
                old_segment = self.confirmed_segments[segment_id]
                # 更新特定段落
                changes.append({
                    "type": "update_segment",
                    "segment_id": segment_id,
                    "old": old_segment["text"],
                    "new": new_text  # 简化处理
                })
        
        return {
            "changes": changes,
            "final_text": self.get_current_text()
        }
```

## 🎯 集成架构

### 主服务类
```python
class EnhancedStreamingService:
    """增强版流式语音识别服务"""
    
    def __init__(self):
        # 核心组件
        self.vad = SileroVAD()
        self.diarization = RealTimeDiarization()
        self.segmentation = IntelligentSegmentation()
        self.update_manager = PartialUpdateManager()
        self.whisper = CrossPlatformWhisper()
        
        # 状态管理
        self.audio_buffer = SlidingWindowBuffer()
        self.processing_queue = asyncio.Queue()
        
    async def process_audio_stream(self, websocket: WebSocket):
        """处理音频流的主要逻辑"""
        
        async for audio_chunk in self._receive_audio(websocket):
            # 1. VAD 检测
            speech_segments = self.vad.detect_speech(audio_chunk)
            
            if not speech_segments:
                continue
            
            # 2. 语音识别
            transcription = await self.whisper.transcribe_async(audio_chunk)
            
            # 3. 说话人分离
            speaker_info = self.diarization.process_chunk(
                audio_chunk, time.time()
            )
            
            # 4. 句子分割和部分更新
            segmentation_result = self.segmentation.process_transcription(
                transcription["text"], 
                transcription.get("confidence", 0.8),
                speech_segments
            )
            
            # 5. 更新管理
            update_result = self.update_manager.process_update(
                segmentation_result["confirmed_sentences"],
                time.time(),
                segmentation_result["confidence_scores"]
            )
            
            # 6. 发送结果
            await self._send_enhanced_result(websocket, {
                "transcription": transcription,
                "speakers": speaker_info,
                "sentences": segmentation_result,
                "updates": update_result
            })
```

## 📊 性能预期

| 功能模块 | 延迟 | 准确率 | 资源占用 |
|----------|------|--------|----------|
| Silero VAD | <50ms | 95%+ | 低 |
| 说话人分离 | <200ms | 90%+ | 中等 |
| 句子分割 | <10ms | 85%+ | 极低 |
| 部分更新 | <20ms | 90%+ | 低 |
| **整体系统** | **<300ms** | **90%+** | **中等** |

## 🚀 实施优先级

### 阶段 1: 基础增强 (1-2 天)
- [x] 集成 Silero VAD
- [ ] 实现智能句子分割
- [ ] 基础部分更新机制

### 阶段 2: 人声分离 (2-3 天)  
- [ ] 集成 Pyannote.audio
- [ ] 实时说话人跟踪
- [ ] 重叠语音处理

### 阶段 3: 高级功能 (1-2 天)
- [ ] 智能置信度评估
- [ ] 复杂部分更新策略
- [ ] 性能优化和调试

这个增强架构将为 WhisperSync 带来专业级的流式语音识别能力！您觉得这个方向如何？我们可以先从哪个模块开始实施？
