import { app, BrowserWindow, ipcMain } from 'electron';
import path from 'path';
import { fileURLToPath } from 'url';
// import { EngineService } from './services/engineService.js';

// 获取当前文件的目录路径（ES模块兼容）
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 引擎配置更新接口已移除 - 当前通过WebSocket直接连接

// 主窗口引用
let mainWindow: BrowserWindow | null = null;
// const engineService = new EngineService(); // 重新禁用，前端直接连接WebSocket

// 创建主窗口
function createWindow(): void {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    title: 'WhisperSync',
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      sandbox: false,
    },
  });

  // 加载开发服务器URL
  mainWindow.loadURL('http://localhost:5173').catch(() => {
    // 如果加载失败，1秒后重试
    setTimeout(() => {
      if (mainWindow) {
        mainWindow.loadURL('http://localhost:5173');
      }
    }, 1000);
  });

  // 打开开发者工具
  mainWindow.webContents.openDevTools();

  // 当窗口关闭时触发
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// 当Electron完成初始化时创建窗口
app.whenReady().then((): void => {
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// 所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 注册IPC处理程序
ipcMain.handle('ping', () => 'pong');

// 音频相关IPC处理
ipcMain.handle('audio:start', async () => {
  console.log('开始录制音频');
  return { success: true };
});

ipcMain.handle('audio:stop', async () => {
  console.log('停止录制音频');
  return { success: true };
});

// 处理音频数据
ipcMain.handle('audio:process', async () => {
  try {
    // 直接转发到引擎服务
    // engineService.sendAudioData(audioData);
    return { success: true };
  } catch (error) {
    console.error('处理音频数据时出错:', error);
    return { success: false, error: (error as Error).message };
  }
});

// 获取最终识别结果
ipcMain.handle('recognition:getFinalResult', async () => {
  try {
    // return engineService.getFinalResult();
    return '';
  } catch {
    // 处理错误
    return '';
  }
});

// 重置识别器
ipcMain.handle('recognition:reset', async () => {
  try {
    // return engineService.resetRecognizer();
    return false;
  } catch {
    // 处理错误
    return false;
  }
});

// 语音识别相关IPC处理
ipcMain.handle('recognition:setLanguage', async () => {
  // return engineService.setRecognitionLanguage(language);
  return true;
});

// 翻译相关IPC处理
ipcMain.handle('translation:setLanguages', async () => {
  // return engineService.setTranslationLanguages(source, target);
  return true;
});

ipcMain.handle('translation:translate', async () => {
  // return engineService.translate(text);
  return '';
});

// 引擎相关IPC处理
ipcMain.handle('engine:setEngine', async () => {
  try {
    // return engineService.setEngine(engineType, backend);
    return true;
  } catch {
    return false;
  }
});

// 获取引擎配置
ipcMain.handle('engine:getConfig', async () => {
  try {
    // 返回默认配置，实际可以从配置文件或环境变量中读取
    return {
      engineType: 'whisper',
      backend: 'auto',
      language: 'zh',
      model: 'base'
    };
  } catch (error) {
    console.error('获取引擎配置失败:', error);
    return {
      engineType: 'whisper',
      backend: 'auto',
      language: 'zh',
      model: 'base'
    };
  }
});

// 获取可用的后端列表
ipcMain.handle('engine:getAvailableBackends', async () => {
  try {
    // 返回支持的后端列表
    return [
      { id: 'auto', name: '自动选择' },
      { id: 'mlx', name: 'MLX (Apple Silicon)' },
      { id: 'faster-whisper', name: 'Faster Whisper (NVIDIA GPU)' },
      { id: 'whisper-timestamped', name: 'Whisper Timestamped (CPU)' }
    ];
  } catch (error) {
    console.error('获取可用后端列表失败:', error);
    return [{ id: 'auto', name: '自动选择' }];
  }
});

// 检查引擎连接状态
ipcMain.handle('engine:checkConnection', async () => {
  // return engineService.isConnected();
  return false;
});

// 获取当前连接状态
ipcMain.handle('engine:getConnectionStatus', async () => {
  try {
    return {
      connected: false,
              url: 'ws://localhost:8889/ws' // 硬编码WebSocket URL
    };
  } catch (error) {
    console.error('获取连接状态出错:', error);
    return { connected: false };
  }
});

// 日志相关IPC处理 - 不输出任何日志
ipcMain.on('console:error', () => {
  // 忽略错误日志
});

// 处理发送到服务器的日志 - 只转发错误日志
ipcMain.on('log:send', (_, logMessage) => {
  // if (!engineService.isConnected) {
  //   return;
  // }

  try {
    // 直接记录错误日志
    console.error(logMessage);

    // 发送到WebSocket服务器
    // 注意：这里需要EngineService提供一个发送自定义消息的方法
    // 暂时注释掉，因为当前EngineService没有提供发送自定义消息的公共方法
    // engineService.send(JSON.stringify(message));
    console.log('Log message (not sent to server):', logMessage);
  } catch (error) {
    console.error('Failed to process log message:', error);
  }
});

// 确保应用是单例的
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // 当运行第二个实例时，将焦点放到主窗口
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}

// 添加连接状态变化监听器
// engineService.on('connection-status', (status) => {
//   if (mainWindow && !mainWindow.isDestroyed()) {
//     mainWindow.webContents.send('connection-status', status);
//   }
// });

// 添加引擎更新事件监听器
// engineService.on('engine:updated', (config: EngineConfigUpdate) => {
//   if (mainWindow && !mainWindow.isDestroyed()) {
//     mainWindow.webContents.send('engine:updated', config);
//   }
// });

// 导出引擎服务实例
// export { engineService };