// WhisperSync Preload Script
// 为渲染进程提供安全的 Electron API 访问

import { contextBridge, ipcRenderer } from 'electron';

// 基础 Electron API
interface ElectronAPI {
  // 基础通信测试
  ping: () => Promise<string>;
}

// 暴露安全的 API 给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 基础通信测试
  ping: () => ipcRenderer.invoke('ping'),
});

// 声明全局类型
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

// 导出类型供其他文件使用
export type { ElectronAPI };